[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\dialog_devicelist.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\dialog_devicelist.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\dialog_serial.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\dialog_serial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\item_floating.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\item_floating.xml"}, {"merged": "com.dp.floating.app-mergeDebugResources-44:/layout/activity_main.xml", "source": "com.dp.floating.app-main-47:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\dialog_gpio.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\dialog_gpio.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\serial_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\serial_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\dialog_gpio_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\dialog_gpio_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\content_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\content_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\my_spinner_textview.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\my_spinner_textview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\device_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\device_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\dialog_devicelist_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\dialog_devicelist_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-mergeDebugResources-44:\\layout\\custom_toast.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.dp.floating.app-main-47:\\layout\\custom_toast.xml"}]