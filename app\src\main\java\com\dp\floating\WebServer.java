package com.dp.floating;

import android.util.Log;
import fi.iki.elonen.NanoHTTPD;
import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

public class WebServer extends NanoHTTPD {
    private static final String TAG = "WebServer";
    private static final int PORT = 8080;

    private  MainActivity mainActivity;
    
    public WebServer(MainActivity smainActivity) {
        super(PORT);
        mainActivity=smainActivity;
    }
    
    @Override
    public Response serve(IHTTPSession session) {
        String uri = session.getUri();
        Method method = session.getMethod();
        
        Log.d(TAG, "Request: " + method + " " + uri);
        
        if (Method.GET.equals(method) && "/".equals(uri)) {
            // 返回主页面
            return newFixedLengthResponse(getMainPage());
        } else if (Method.POST.equals(method) && "/button-click".equals(uri)) {
            // 处理按钮点击
            return handleButtonClick(session);
        }
        
        return newFixedLengthResponse(Response.Status.NOT_FOUND, MIME_PLAINTEXT, "Not Found");
    }
    
    private Response handleButtonClick(IHTTPSession session) {
        try {
            Map<String, String> files = new HashMap<>();
            session.parseBody(files);
            Map<String, String> params = session.getParms();

            String row = params.get("row");
            String col = params.get("col");

            if (row != null && col != null) {
                String message = "按钮点击 - 行: " + row + ", 列: " + col;
                Log.i(TAG, message);
                if(mainActivity!=null){
                    int temprow = Integer.parseInt(row)+1; ;
                    int tempcol = Integer.parseInt(col)+1;
                    String a = "push_bat "+tempcol+" "+temprow+" \n \n";
                    mainActivity.send(a);
                }

                return newFixedLengthResponse(Response.Status.OK, MIME_PLAINTEXT, "OK");
            }
        } catch (Exception e) {
            Log.e(TAG, "处理按钮点击时出错", e);
        }

        return newFixedLengthResponse(Response.Status.BAD_REQUEST, MIME_PLAINTEXT, "Bad Request");
    }
    
    private String getMainPage() {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>按钮控制面板</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            font-family: Arial, sans-serif;\n" +
                "            margin: 20px;\n" +
                "            background-color: #f0f0f0;\n" +
                "        }\n" +
                "        .container {\n" +
                "            max-width: 800px;\n" +
                "            margin: 0 auto;\n" +
                "            background-color: white;\n" +
                "            padding: 20px;\n" +
                "            border-radius: 10px;\n" +
                "            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n" +
                "        }\n" +
                "        h1 {\n" +
                "            text-align: center;\n" +
                "            color: #333;\n" +
                "            margin-bottom: 30px;\n" +
                "        }\n" +
                "        .grid {\n" +
                "            display: grid;\n" +
                "            grid-template-columns: repeat(10, 1fr);\n" +
                "            gap: 5px;\n" +
                "            margin: 20px 0;\n" +
                "        }\n" +
                "        .btn {\n" +
                "            padding: 15px;\n" +
                "            border: 2px solid #007bff;\n" +
                "            background-color: #007bff;\n" +
                "            color: white;\n" +
                "            border-radius: 5px;\n" +
                "            cursor: pointer;\n" +
                "            font-size: 12px;\n" +
                "            font-weight: bold;\n" +
                "            transition: all 0.3s ease;\n" +
                "            min-height: 40px;\n" +
                "            display: flex;\n" +
                "            align-items: center;\n" +
                "            justify-content: center;\n" +
                "        }\n" +
                "        .btn:hover {\n" +
                "            background-color: #0056b3;\n" +
                "            border-color: #0056b3;\n" +
                "            transform: translateY(-2px);\n" +
                "        }\n" +
                "        .btn:active {\n" +
                "            transform: translateY(0);\n" +
                "            background-color: #004085;\n" +
                "        }\n" +
                "        .status {\n" +
                "            text-align: center;\n" +
                "            margin-top: 20px;\n" +
                "            padding: 10px;\n" +
                "            background-color: #e9ecef;\n" +
                "            border-radius: 5px;\n" +
                "            font-weight: bold;\n" +
                "        }\n" +
                "        @media (max-width: 768px) {\n" +
                "            .grid {\n" +
                "                grid-template-columns: repeat(5, 1fr);\n" +
                "            }\n" +
                "            .btn {\n" +
                "                padding: 10px;\n" +
                "                font-size: 10px;\n" +
                "            }\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1>按钮控制面板 (10×5)</h1>\n" +
                "        <div class=\"grid\" id=\"buttonGrid\">\n" +
                "        </div>\n" +
                "        <div class=\"status\" id=\"status\">准备就绪</div>\n" +
                "    </div>\n" +
                "\n" +
                "    <script>\n" +
                "        // 创建10x5的按钮网格\n" +
                "        function createGrid() {\n" +
                "            const grid = document.getElementById('buttonGrid');\n" +
                "            \n" +
                "            for (let row = 0; row < 5; row++) {\n" +
                "                for (let col = 0; col < 10; col++) {\n" +
                "                    const button = document.createElement('button');\n" +
                "                    button.className = 'btn';\n" +
                "                    button.textContent = `(${row},${col})`;\n" +
                "                    button.onclick = () => clickButton(row, col);\n" +
                "                    grid.appendChild(button);\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "        // 处理按钮点击\n" +
                "        function clickButton(row, col) {\n" +
                "            const status = document.getElementById('status');\n" +
                "            status.textContent = `正在发送点击事件: 行${row}, 列${col}...`;\n" +
                "            \n" +
                "            fetch('/button-click', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/x-www-form-urlencoded',\n" +
                "                },\n" +
                "                body: `row=${row}&col=${col}`\n" +
                "            })\n" +
                "            .then(response => {\n" +
                "                if (response.ok) {\n" +
                "                    status.textContent = `成功发送: 行${row}, 列${col}`;\n" +
                "                    setTimeout(() => {\n" +
                "                        status.textContent = '准备就绪';\n" +
                "                    }, 2000);\n" +
                "                } else {\n" +
                "                    status.textContent = '发送失败';\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('Error:', error);\n" +
                "                status.textContent = '网络错误';\n" +
                "            });\n" +
                "        }\n" +
                "\n" +
                "        // 页面加载完成后创建网格\n" +
                "        document.addEventListener('DOMContentLoaded', createGrid);\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }
    
    public void startServer() {
        try {
            start(NanoHTTPD.SOCKET_READ_TIMEOUT, false);
            String localIP = getLocalIPAddress();
            Log.i(TAG, "Web服务器已启动，端口: " + PORT);
            Log.i(TAG, "本地访问地址: http://localhost:" + PORT);
            if (localIP != null) {
                Log.i(TAG, "网络访问地址: http://" + localIP + ":" + PORT);
            }
        } catch (IOException e) {
            Log.e(TAG, "启动Web服务器失败", e);
        }
    }
    
    public void stopServer() {
        stop();
        Log.i(TAG, "Web服务器已停止");
    }

    private String getLocalIPAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements();) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && !inetAddress.isLinkLocalAddress()) {
                        String ip = inetAddress.getHostAddress();
                        // 过滤IPv6地址，只返回IPv4地址
                        if (ip != null && ip.indexOf(':') == -1) {
                            return ip;
                        }
                    }
                }
            }
        } catch (SocketException ex) {
            Log.e(TAG, "获取IP地址失败", ex);
        }
        return null;
    }
}
