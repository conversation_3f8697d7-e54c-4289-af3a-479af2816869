<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.dp.floating" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="53"/></Target><Target tag="layout/activity_main_0" include="content_main"><Expressions/><location startLine="21" startOffset="4" endLine="21" endOffset="44"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="14" startOffset="8" endLine="17" endOffset="57"/></Target><Target id="@+id/button" view="Button"><Expressions/><location startLine="34" startOffset="4" endLine="41" endOffset="12"/></Target><Target id="@+id/send" view="Button"><Expressions/><location startLine="44" startOffset="4" endLine="51" endOffset="12"/></Target><Target id="@+id/webServerBtn" view="Button"><Expressions/><location startLine="53" startOffset="4" endLine="59" endOffset="12"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="63" startOffset="4" endLine="70" endOffset="59"/></Target></Targets></Layout>