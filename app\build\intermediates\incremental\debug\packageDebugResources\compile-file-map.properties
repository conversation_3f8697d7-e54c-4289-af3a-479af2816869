#Wed Jun 25 17:01:23 CST 2025
com.dp.floating.app-main-5\:/drawable/aaa.png=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\aaa.png
com.dp.floating.app-main-5\:/drawable/bg_spinner.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_spinner.xml
com.dp.floating.app-main-5\:/drawable/btn_round.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\btn_round.xml
com.dp.floating.app-main-5\:/drawable/ic_launcher.jpg=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher.jpg
com.dp.floating.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.dp.floating.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.dp.floating.app-main-5\:/drawable/ll_border.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ll_border.xml
com.dp.floating.app-main-5\:/drawable/ll_round.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ll_round.xml
com.dp.floating.app-main-5\:/drawable/ll_round_black.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ll_round_black.xml
com.dp.floating.app-main-5\:/menu/menu_main.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_main.xml
com.dp.floating.app-main-5\:/mipmap-anydpi/ic_launcher.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.dp.floating.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.dp.floating.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.dp.floating.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.dp.floating.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.dp.floating.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.dp.floating.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.dp.floating.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.dp.floating.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.dp.floating.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.dp.floating.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.dp.floating.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.dp.floating.app-main-5\:/navigation/nav_graph.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\nav_graph.xml
com.dp.floating.app-main-5\:/xml/backup_rules.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.dp.floating.app-main-5\:/xml/data_extraction_rules.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.dp.floating.app-main-5\:/xml/device_filter.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\device_filter.xml
com.dp.floating.app-packageDebugResources-2\:/layout/activity_main.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.dp.floating.app-packageDebugResources-2\:/layout/content_main.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\content_main.xml
com.dp.floating.app-packageDebugResources-2\:/layout/custom_toast.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\custom_toast.xml
com.dp.floating.app-packageDebugResources-2\:/layout/device_item.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\device_item.xml
com.dp.floating.app-packageDebugResources-2\:/layout/dialog_devicelist.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_devicelist.xml
com.dp.floating.app-packageDebugResources-2\:/layout/dialog_devicelist_item.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_devicelist_item.xml
com.dp.floating.app-packageDebugResources-2\:/layout/dialog_gpio.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_gpio.xml
com.dp.floating.app-packageDebugResources-2\:/layout/dialog_gpio_item.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_gpio_item.xml
com.dp.floating.app-packageDebugResources-2\:/layout/dialog_serial.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_serial.xml
com.dp.floating.app-packageDebugResources-2\:/layout/item_floating.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_floating.xml
com.dp.floating.app-packageDebugResources-2\:/layout/my_spinner_textview.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\my_spinner_textview.xml
com.dp.floating.app-packageDebugResources-2\:/layout/serial_item.xml=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\serial_item.xml
