<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardItemFx"
    android:layout_width="360dp"
    android:layout_height="350dp"
    >
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/aaa"/>
    <TextView
        android:id="@+id/tvItemFx"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="top|end"
        android:padding="7dp"
        android:text=""
        android:textColor="@color/white"
        android:textSize="14sp" />
</LinearLayout>