// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import com.dp.floating.ui.CustomTextView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SerialItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatCheckBox cbBreak;

  @NonNull
  public final AppCompatCheckBox cbCTS;

  @NonNull
  public final AppCompatCheckBox cbDCD;

  @NonNull
  public final AppCompatCheckBox cbDSR;

  @NonNull
  public final AppCompatCheckBox cbDTR;

  @NonNull
  public final AppCompatCheckBox cbFrame;

  @NonNull
  public final AppCompatCheckBox cbOverrun;

  @NonNull
  public final AppCompatCheckBox cbParity;

  @NonNull
  public final AppCompatCheckBox cbRTS;

  @NonNull
  public final AppCompatCheckBox cbRing;

  @NonNull
  public final CustomTextView queryErrorStatus;

  @NonNull
  public final SwitchCompat scWrite;

  @NonNull
  public final EditText sendData;

  @NonNull
  public final CustomTextView tvClearWrite;

  @NonNull
  public final CustomTextView tvSerialConfig;

  @NonNull
  public final TextView tvSerialDescription;

  @NonNull
  public final TextView tvSerialInfo;

  @NonNull
  public final CustomTextView tvWrite;

  @NonNull
  public final TextView tvWriteCount;

  private SerialItemBinding(@NonNull LinearLayout rootView, @NonNull AppCompatCheckBox cbBreak,
      @NonNull AppCompatCheckBox cbCTS, @NonNull AppCompatCheckBox cbDCD,
      @NonNull AppCompatCheckBox cbDSR, @NonNull AppCompatCheckBox cbDTR,
      @NonNull AppCompatCheckBox cbFrame, @NonNull AppCompatCheckBox cbOverrun,
      @NonNull AppCompatCheckBox cbParity, @NonNull AppCompatCheckBox cbRTS,
      @NonNull AppCompatCheckBox cbRing, @NonNull CustomTextView queryErrorStatus,
      @NonNull SwitchCompat scWrite, @NonNull EditText sendData,
      @NonNull CustomTextView tvClearWrite, @NonNull CustomTextView tvSerialConfig,
      @NonNull TextView tvSerialDescription, @NonNull TextView tvSerialInfo,
      @NonNull CustomTextView tvWrite, @NonNull TextView tvWriteCount) {
    this.rootView = rootView;
    this.cbBreak = cbBreak;
    this.cbCTS = cbCTS;
    this.cbDCD = cbDCD;
    this.cbDSR = cbDSR;
    this.cbDTR = cbDTR;
    this.cbFrame = cbFrame;
    this.cbOverrun = cbOverrun;
    this.cbParity = cbParity;
    this.cbRTS = cbRTS;
    this.cbRing = cbRing;
    this.queryErrorStatus = queryErrorStatus;
    this.scWrite = scWrite;
    this.sendData = sendData;
    this.tvClearWrite = tvClearWrite;
    this.tvSerialConfig = tvSerialConfig;
    this.tvSerialDescription = tvSerialDescription;
    this.tvSerialInfo = tvSerialInfo;
    this.tvWrite = tvWrite;
    this.tvWriteCount = tvWriteCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SerialItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SerialItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.serial_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SerialItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbBreak;
      AppCompatCheckBox cbBreak = ViewBindings.findChildViewById(rootView, id);
      if (cbBreak == null) {
        break missingId;
      }

      id = R.id.cbCTS;
      AppCompatCheckBox cbCTS = ViewBindings.findChildViewById(rootView, id);
      if (cbCTS == null) {
        break missingId;
      }

      id = R.id.cbDCD;
      AppCompatCheckBox cbDCD = ViewBindings.findChildViewById(rootView, id);
      if (cbDCD == null) {
        break missingId;
      }

      id = R.id.cbDSR;
      AppCompatCheckBox cbDSR = ViewBindings.findChildViewById(rootView, id);
      if (cbDSR == null) {
        break missingId;
      }

      id = R.id.cbDTR;
      AppCompatCheckBox cbDTR = ViewBindings.findChildViewById(rootView, id);
      if (cbDTR == null) {
        break missingId;
      }

      id = R.id.cbFrame;
      AppCompatCheckBox cbFrame = ViewBindings.findChildViewById(rootView, id);
      if (cbFrame == null) {
        break missingId;
      }

      id = R.id.cbOverrun;
      AppCompatCheckBox cbOverrun = ViewBindings.findChildViewById(rootView, id);
      if (cbOverrun == null) {
        break missingId;
      }

      id = R.id.cbParity;
      AppCompatCheckBox cbParity = ViewBindings.findChildViewById(rootView, id);
      if (cbParity == null) {
        break missingId;
      }

      id = R.id.cbRTS;
      AppCompatCheckBox cbRTS = ViewBindings.findChildViewById(rootView, id);
      if (cbRTS == null) {
        break missingId;
      }

      id = R.id.cbRing;
      AppCompatCheckBox cbRing = ViewBindings.findChildViewById(rootView, id);
      if (cbRing == null) {
        break missingId;
      }

      id = R.id.queryErrorStatus;
      CustomTextView queryErrorStatus = ViewBindings.findChildViewById(rootView, id);
      if (queryErrorStatus == null) {
        break missingId;
      }

      id = R.id.scWrite;
      SwitchCompat scWrite = ViewBindings.findChildViewById(rootView, id);
      if (scWrite == null) {
        break missingId;
      }

      id = R.id.send_data;
      EditText sendData = ViewBindings.findChildViewById(rootView, id);
      if (sendData == null) {
        break missingId;
      }

      id = R.id.tvClearWrite;
      CustomTextView tvClearWrite = ViewBindings.findChildViewById(rootView, id);
      if (tvClearWrite == null) {
        break missingId;
      }

      id = R.id.tvSerialConfig;
      CustomTextView tvSerialConfig = ViewBindings.findChildViewById(rootView, id);
      if (tvSerialConfig == null) {
        break missingId;
      }

      id = R.id.tvSerialDescription;
      TextView tvSerialDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvSerialDescription == null) {
        break missingId;
      }

      id = R.id.tvSerialInfo;
      TextView tvSerialInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvSerialInfo == null) {
        break missingId;
      }

      id = R.id.tvWrite;
      CustomTextView tvWrite = ViewBindings.findChildViewById(rootView, id);
      if (tvWrite == null) {
        break missingId;
      }

      id = R.id.tvWriteCount;
      TextView tvWriteCount = ViewBindings.findChildViewById(rootView, id);
      if (tvWriteCount == null) {
        break missingId;
      }

      return new SerialItemBinding((LinearLayout) rootView, cbBreak, cbCTS, cbDCD, cbDSR, cbDTR,
          cbFrame, cbOverrun, cbParity, cbRTS, cbRing, queryErrorStatus, scWrite, sendData,
          tvClearWrite, tvSerialConfig, tvSerialDescription, tvSerialInfo, tvWrite, tvWriteCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
