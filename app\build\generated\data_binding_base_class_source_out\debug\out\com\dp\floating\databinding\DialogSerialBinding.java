// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSerialBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button cancel;

  @NonNull
  public final SwitchCompat scFLow;

  @NonNull
  public final Button set;

  @NonNull
  public final Spinner spinnerBaud;

  @NonNull
  public final Spinner spinnerData;

  @NonNull
  public final Spinner spinnerParity;

  @NonNull
  public final Spinner spinnerStop;

  private DialogSerialBinding(@NonNull LinearLayout rootView, @NonNull Button cancel,
      @NonNull SwitchCompat scFLow, @NonNull Button set, @NonNull Spinner spinnerBaud,
      @NonNull Spinner spinnerData, @NonNull Spinner spinnerParity, @NonNull Spinner spinnerStop) {
    this.rootView = rootView;
    this.cancel = cancel;
    this.scFLow = scFLow;
    this.set = set;
    this.spinnerBaud = spinnerBaud;
    this.spinnerData = spinnerData;
    this.spinnerParity = spinnerParity;
    this.spinnerStop = spinnerStop;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSerialBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSerialBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_serial, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSerialBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancel;
      Button cancel = ViewBindings.findChildViewById(rootView, id);
      if (cancel == null) {
        break missingId;
      }

      id = R.id.scFLow;
      SwitchCompat scFLow = ViewBindings.findChildViewById(rootView, id);
      if (scFLow == null) {
        break missingId;
      }

      id = R.id.set;
      Button set = ViewBindings.findChildViewById(rootView, id);
      if (set == null) {
        break missingId;
      }

      id = R.id.spinner_baud;
      Spinner spinnerBaud = ViewBindings.findChildViewById(rootView, id);
      if (spinnerBaud == null) {
        break missingId;
      }

      id = R.id.spinner_data;
      Spinner spinnerData = ViewBindings.findChildViewById(rootView, id);
      if (spinnerData == null) {
        break missingId;
      }

      id = R.id.spinner_parity;
      Spinner spinnerParity = ViewBindings.findChildViewById(rootView, id);
      if (spinnerParity == null) {
        break missingId;
      }

      id = R.id.spinner_stop;
      Spinner spinnerStop = ViewBindings.findChildViewById(rootView, id);
      if (spinnerStop == null) {
        break missingId;
      }

      return new DialogSerialBinding((LinearLayout) rootView, cancel, scFLow, set, spinnerBaud,
          spinnerData, spinnerParity, spinnerStop);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
