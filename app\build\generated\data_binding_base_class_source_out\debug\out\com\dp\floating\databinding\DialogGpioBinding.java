// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogGpioBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView rvGPIO;

  @NonNull
  public final TextView tvClose;

  @NonNull
  public final TextView tvConfigGPIO;

  @NonNull
  public final TextView tvDes;

  @NonNull
  public final TextView tvGetAllGPIO;

  @NonNull
  public final TextView tvGetGPIOVal;

  @NonNull
  public final TextView tvSetGPIOVal;

  private DialogGpioBinding(@NonNull LinearLayout rootView, @NonNull RecyclerView rvGPIO,
      @NonNull TextView tvClose, @NonNull TextView tvConfigGPIO, @NonNull TextView tvDes,
      @NonNull TextView tvGetAllGPIO, @NonNull TextView tvGetGPIOVal,
      @NonNull TextView tvSetGPIOVal) {
    this.rootView = rootView;
    this.rvGPIO = rvGPIO;
    this.tvClose = tvClose;
    this.tvConfigGPIO = tvConfigGPIO;
    this.tvDes = tvDes;
    this.tvGetAllGPIO = tvGetAllGPIO;
    this.tvGetGPIOVal = tvGetGPIOVal;
    this.tvSetGPIOVal = tvSetGPIOVal;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogGpioBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogGpioBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_gpio, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogGpioBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rvGPIO;
      RecyclerView rvGPIO = ViewBindings.findChildViewById(rootView, id);
      if (rvGPIO == null) {
        break missingId;
      }

      id = R.id.tv_close;
      TextView tvClose = ViewBindings.findChildViewById(rootView, id);
      if (tvClose == null) {
        break missingId;
      }

      id = R.id.tvConfigGPIO;
      TextView tvConfigGPIO = ViewBindings.findChildViewById(rootView, id);
      if (tvConfigGPIO == null) {
        break missingId;
      }

      id = R.id.tvDes;
      TextView tvDes = ViewBindings.findChildViewById(rootView, id);
      if (tvDes == null) {
        break missingId;
      }

      id = R.id.tvGetAllGPIO;
      TextView tvGetAllGPIO = ViewBindings.findChildViewById(rootView, id);
      if (tvGetAllGPIO == null) {
        break missingId;
      }

      id = R.id.tvGetGPIOVal;
      TextView tvGetGPIOVal = ViewBindings.findChildViewById(rootView, id);
      if (tvGetGPIOVal == null) {
        break missingId;
      }

      id = R.id.tvSetGPIOVal;
      TextView tvSetGPIOVal = ViewBindings.findChildViewById(rootView, id);
      if (tvSetGPIOVal == null) {
        break missingId;
      }

      return new DialogGpioBinding((LinearLayout) rootView, rvGPIO, tvClose, tvConfigGPIO, tvDes,
          tvGetAllGPIO, tvGetGPIOVal, tvSetGPIOVal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
