// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DeviceItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnClose;

  @NonNull
  public final RecyclerView rvSerial;

  @NonNull
  public final TextView tvDescription;

  private DeviceItemBinding(@NonNull LinearLayout rootView, @NonNull Button btnClose,
      @NonNull RecyclerView rvSerial, @NonNull TextView tvDescription) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.rvSerial = rvSerial;
    this.tvDescription = tvDescription;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DeviceItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DeviceItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.device_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DeviceItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClose;
      Button btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.rvSerial;
      RecyclerView rvSerial = ViewBindings.findChildViewById(rootView, id);
      if (rvSerial == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      return new DeviceItemBinding((LinearLayout) rootView, btnClose, rvSerial, tvDescription);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
