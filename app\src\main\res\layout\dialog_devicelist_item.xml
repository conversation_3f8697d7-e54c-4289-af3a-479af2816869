<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="10dp"
        android:layout_marginTop="10dp"
        >
        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="0.7"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >
            <TextView
                android:id="@+id/name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textSize="@dimen/sp_15"
                android:textColor="#000000"
                android:gravity="start"
                />
            <TextView
                android:id="@+id/id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textSize="@dimen/sp_15"
                android:textColor="#000000"
                android:gravity="start"
                />
        </LinearLayout>

        <TextView
            android:id="@+id/type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textSize="@dimen/sp_15"
            android:textColor="#000000"
            android:gravity="start"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            />
    </LinearLayout>
</LinearLayout>
