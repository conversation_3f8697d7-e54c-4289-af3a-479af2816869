<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:minWidth="@dimen/dp_250">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_10"
        android:background="@drawable/ll_round"
        android:layout_gravity="center"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dp_10"
            android:layout_marginStart="@dimen/dp_10"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_15"

            >
            <TextView
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="串口参数配置"
                android:textColor="@color/black"
                android:textSize="@dimen/sp_15"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/ll_border"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_10"
                >
                <TextView
                    android:text="波特率"
                    android:textColor="@color/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    />
                <Spinner
                    android:id="@+id/spinner_baud"
                    android:layout_marginStart="@dimen/dp_5"
                    android:layout_width="@dimen/dp_90"
                    android:layout_height="@dimen/dp_30"
                    android:background="@drawable/bg_spinner"
                    android:gravity="center_horizontal|center"
                    android:spinnerMode="dropdown"
                    />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                >
                <TextView
                    android:text="数据位"
                    android:textColor="@color/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    />
                <Spinner
                    android:id="@+id/spinner_data"
                    android:layout_marginStart="@dimen/dp_5"
                    android:layout_width="@dimen/dp_90"
                    android:layout_height="@dimen/dp_30"
                    android:background="@drawable/bg_spinner"
                    android:gravity="center_horizontal|center"
                    android:spinnerMode="dropdown"
                    />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                >
                <TextView
                    android:text="停止位"
                    android:textColor="@color/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"/>
                <Spinner
                    android:id="@+id/spinner_stop"
                    android:layout_marginStart="@dimen/dp_5"
                    android:layout_width="@dimen/dp_90"
                    android:layout_height="@dimen/dp_30"
                    android:background="@drawable/bg_spinner"
                    android:gravity="center_horizontal|center"
                    android:spinnerMode="dropdown"
                    />
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"
                >
                <TextView
                    android:text="校验位"
                    android:textColor="@color/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"/>
                <Spinner
                    android:id="@+id/spinner_parity"
                    android:layout_marginStart="@dimen/dp_5"
                    android:layout_width="@dimen/dp_90"
                    android:layout_height="@dimen/dp_30"
                    android:layout_gravity="center"
                    android:background="@drawable/bg_spinner"
                    android:gravity="center_horizontal|center"
                    android:spinnerMode="dropdown"
                    />
                <LinearLayout
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="@dimen/dp_10"
                android:layout_marginTop="@dimen/dp_5"

                >
                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/scFLow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:text="流控开关"
                    app:switchMinWidth="@dimen/dp_50"
                    />

            </LinearLayout>
            <Button
                android:id="@+id/set"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp_5"
                android:text="设置"
                android:textColor="@color/white"
                android:background="@drawable/btn_round"
                style="?android:attr/borderlessButtonStyle"
                android:textSize="@dimen/sp_13"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp_15"


            >
            <Button
                android:id="@+id/cancel"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_40"
                android:layout_gravity="center"
                android:text="关闭"
                android:textColor="@color/white"
                android:background="@drawable/btn_round"
                style="?android:attr/borderlessButtonStyle"
                android:textSize="@dimen/sp_13"
                />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>
