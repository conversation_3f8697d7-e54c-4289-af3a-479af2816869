-- Merging decision tree log ---
manifest
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:2:1-74:12
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:2:1-74:12
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:2:1-74:12
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:2:1-74:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9785a0ed0d5f32df6df1ed78caaa8e0\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.petterpx:floatingx-compose:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee9d7940ba7f90225e5cd4e06c7e37c\transformed\floatingx-compose-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98a659a597737475e7f02ba3f2713c3b\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdf0b27407259d4857f29c19e30f073\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cbc0721f559aaa4a22da3fb716ae80d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd689883b32bb47b2e2029fd27c5dcaa\transformed\navigation-ui-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97e51786a577cfd88c7bb18737c70d4c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f75e7b09c2ee3a75e0568743724e8f28\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49392cdabfaf049fa6c900121fc25c07\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba14df3a11028ce01d9122b0262479c1\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f413b3ec5d710d2efcfa860d7977dc7e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ff55178c6a75b99b80eae3fdfb086c\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c32ae91b7d7c1bfc67712aa2b4b4a43\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc9dcf2e1fb13ebe4b6c55e8bf45c93\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47af5e61f9b550275fbc3a352cf8245f\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94a7c77c26a0b5e1a9a196c6da20b041\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8443cb2ddf5be970aa3a53b33046ff38\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78ede33af7eb21a0d6769a0aff67a6ad\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54735e0d8116642e013ce433f425f1ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5247a6d8523a389eee69a0bd356007b\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdf137709e2050ad2a999000402dcffe\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f983be70d4a9b6ece1d0755c9cf63e18\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c2d079906ddff2c162e0fbbbd540414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dfcef6c252cf12d76965f2cb63f925\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182b6a52038bd0ede3afbdeba6f80fe4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a4109d37f23fc1e9d0cc4df39a53959\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eece4432a95d1a45f9c8110836cb2d5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0aba0ec4c8b5ccb17a2e45177cebdda\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b67c049f600c87a604783f97afbe223d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68c516fd52be971bc76ec635382f49d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3b5e3089142fbbe2f194cda7595574e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8c5bf79c56e79503cd3cd63b475dfef\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702bff04df6ae2f51727b4bf92d06bfe\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410134e022ebc6dd19a3288a63aee27e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd383e05b557a379b33b691991abab0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afd32e02025464765b1997e15a1d9b2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\594aff5d1bbd7b41888951bd17eb51a8\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b18a750ef4876d552be33baf82a4889\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a6df5615bc5203b756a623c6e1ce61\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82ee94050a90fe508bb5b6abc0c5a804\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e4fc39584ffa73db8858010c5bd117\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af5e3f61d1500feb965b783abfbd3835\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bfcdeec2f0a0251b0073c126e784d0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d881e1afa8ee5c27202d0def8b4fdd3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\893218bc63e981826e99e251aa32a15c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\676d2931f5f01a3b7ed489c5720be73f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4988fe8f28a02e65445df22a06ea79b\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49d79478fde7022af3229d59cf5bab81\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2245dac4b005ae62e13608ce458624\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588122b74d6b72b5b25880419d565f1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73338bb6500c9eea31fce4b13b7d1e4d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:5-78
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:22-75
uses-permission#android.permission.SYSTEM_OVERLAY_WINDOW
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.hardware.usb.accessory
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:5-70
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:22-67
uses-permission#android.hardware.usb.host
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:22-62
uses-permission#android.permission.INTERNET
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:5-67
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:22-76
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:5-77
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:22-65
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:18:22-78
uses-feature#android.hardware.usb.host
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:20:5-22:35
	android:required
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:22:9-32
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:21:9-49
application
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:24:5-72:19
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:24:5-72:19
MERGED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:7:5-13:19
MERGED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97e51786a577cfd88c7bb18737c70d4c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97e51786a577cfd88c7bb18737c70d4c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f75e7b09c2ee3a75e0568743724e8f28\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f75e7b09c2ee3a75e0568743724e8f28\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bfcdeec2f0a0251b0073c126e784d0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bfcdeec2f0a0251b0073c126e784d0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:30:9-41
	android:fullBackupContent
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:31:9-54
	tools:targetApi
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:34:9-29
	android:icon
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:29:9-45
	android:allowBackup
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:33:9-46
	android:dataExtractionRules
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:27:9-65
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:25:9-46
activity#com.dp.floating.MainActivity
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:35:9-44:20
	android:exported
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:37:13-36
	android:theme
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:38:13-50
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:36:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:39:13-43:29
action#android.intent.action.MAIN
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:40:17-69
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:40:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:17-77
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:27-74
receiver#com.dp.floating.service.LauncherReceiver
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:47:9-54:20
	android:exported
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:49:13-36
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:48:13-53
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+category:name:android.intent.category.LAUNCHER
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:50:13-53:29
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:51:17-79
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:51:25-76
service#com.dp.floating.service.LauncherService
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:56:9-64:19
	android:process
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:60:13-47
	android:enabled
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:58:13-35
	android:exported
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:59:13-36
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:57:13-52
intent-filter#action:name:com.test.Service
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:61:13-63:29
action#com.test.Service
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:62:17-59
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:62:25-56
service#com.dp.floating.service.BackgroundService
ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:66:9-71:19
	android:enabled
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:68:13-35
	android:exported
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:69:13-37
	android:foregroundServiceType
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:70:13-53
	android:name
		ADDED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml:67:13-54
uses-sdk
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9785a0ed0d5f32df6df1ed78caaa8e0\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9785a0ed0d5f32df6df1ed78caaa8e0\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.petterpx:floatingx-compose:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee9d7940ba7f90225e5cd4e06c7e37c\transformed\floatingx-compose-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [io.github.petterpx:floatingx-compose:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aee9d7940ba7f90225e5cd4e06c7e37c\transformed\floatingx-compose-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98a659a597737475e7f02ba3f2713c3b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\98a659a597737475e7f02ba3f2713c3b\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdf0b27407259d4857f29c19e30f073\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdf0b27407259d4857f29c19e30f073\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cbc0721f559aaa4a22da3fb716ae80d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5cbc0721f559aaa4a22da3fb716ae80d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd689883b32bb47b2e2029fd27c5dcaa\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd689883b32bb47b2e2029fd27c5dcaa\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97e51786a577cfd88c7bb18737c70d4c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97e51786a577cfd88c7bb18737c70d4c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f75e7b09c2ee3a75e0568743724e8f28\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f75e7b09c2ee3a75e0568743724e8f28\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49392cdabfaf049fa6c900121fc25c07\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49392cdabfaf049fa6c900121fc25c07\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba14df3a11028ce01d9122b0262479c1\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba14df3a11028ce01d9122b0262479c1\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f413b3ec5d710d2efcfa860d7977dc7e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f413b3ec5d710d2efcfa860d7977dc7e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ff55178c6a75b99b80eae3fdfb086c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ff55178c6a75b99b80eae3fdfb086c\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c32ae91b7d7c1bfc67712aa2b4b4a43\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c32ae91b7d7c1bfc67712aa2b4b4a43\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc9dcf2e1fb13ebe4b6c55e8bf45c93\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cc9dcf2e1fb13ebe4b6c55e8bf45c93\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47af5e61f9b550275fbc3a352cf8245f\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47af5e61f9b550275fbc3a352cf8245f\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94a7c77c26a0b5e1a9a196c6da20b041\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94a7c77c26a0b5e1a9a196c6da20b041\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8443cb2ddf5be970aa3a53b33046ff38\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8443cb2ddf5be970aa3a53b33046ff38\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78ede33af7eb21a0d6769a0aff67a6ad\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78ede33af7eb21a0d6769a0aff67a6ad\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54735e0d8116642e013ce433f425f1ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54735e0d8116642e013ce433f425f1ef\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5247a6d8523a389eee69a0bd356007b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5247a6d8523a389eee69a0bd356007b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdf137709e2050ad2a999000402dcffe\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdf137709e2050ad2a999000402dcffe\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f983be70d4a9b6ece1d0755c9cf63e18\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f983be70d4a9b6ece1d0755c9cf63e18\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c2d079906ddff2c162e0fbbbd540414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c2d079906ddff2c162e0fbbbd540414\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dfcef6c252cf12d76965f2cb63f925\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6dfcef6c252cf12d76965f2cb63f925\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182b6a52038bd0ede3afbdeba6f80fe4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182b6a52038bd0ede3afbdeba6f80fe4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a4109d37f23fc1e9d0cc4df39a53959\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a4109d37f23fc1e9d0cc4df39a53959\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eece4432a95d1a45f9c8110836cb2d5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0eece4432a95d1a45f9c8110836cb2d5\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0aba0ec4c8b5ccb17a2e45177cebdda\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0aba0ec4c8b5ccb17a2e45177cebdda\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b67c049f600c87a604783f97afbe223d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b67c049f600c87a604783f97afbe223d\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68c516fd52be971bc76ec635382f49d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\68c516fd52be971bc76ec635382f49d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3b5e3089142fbbe2f194cda7595574e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3b5e3089142fbbe2f194cda7595574e\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8c5bf79c56e79503cd3cd63b475dfef\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8c5bf79c56e79503cd3cd63b475dfef\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702bff04df6ae2f51727b4bf92d06bfe\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702bff04df6ae2f51727b4bf92d06bfe\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410134e022ebc6dd19a3288a63aee27e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410134e022ebc6dd19a3288a63aee27e\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd383e05b557a379b33b691991abab0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbd383e05b557a379b33b691991abab0\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afd32e02025464765b1997e15a1d9b2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afd32e02025464765b1997e15a1d9b2e\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\594aff5d1bbd7b41888951bd17eb51a8\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\594aff5d1bbd7b41888951bd17eb51a8\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b18a750ef4876d552be33baf82a4889\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b18a750ef4876d552be33baf82a4889\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a6df5615bc5203b756a623c6e1ce61\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25a6df5615bc5203b756a623c6e1ce61\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82ee94050a90fe508bb5b6abc0c5a804\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82ee94050a90fe508bb5b6abc0c5a804\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e4fc39584ffa73db8858010c5bd117\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e4fc39584ffa73db8858010c5bd117\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af5e3f61d1500feb965b783abfbd3835\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af5e3f61d1500feb965b783abfbd3835\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bfcdeec2f0a0251b0073c126e784d0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bfcdeec2f0a0251b0073c126e784d0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d881e1afa8ee5c27202d0def8b4fdd3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d881e1afa8ee5c27202d0def8b4fdd3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\893218bc63e981826e99e251aa32a15c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\893218bc63e981826e99e251aa32a15c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\676d2931f5f01a3b7ed489c5720be73f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\676d2931f5f01a3b7ed489c5720be73f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4988fe8f28a02e65445df22a06ea79b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4988fe8f28a02e65445df22a06ea79b\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49d79478fde7022af3229d59cf5bab81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49d79478fde7022af3229d59cf5bab81\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2245dac4b005ae62e13608ce458624\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba2245dac4b005ae62e13608ce458624\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588122b74d6b72b5b25880419d565f1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5588122b74d6b72b5b25880419d565f1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73338bb6500c9eea31fce4b13b7d1e4d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73338bb6500c9eea31fce4b13b7d1e4d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\code\andriod\floating\app\src\main\AndroidManifest.xml
provider#com.petterp.floatingx.assist.FxContentProvider
ADDED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:8:9-12:43
	android:authorities
		ADDED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:10:13-63
	android:multiprocess
		ADDED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:12:13-40
	android:exported
		ADDED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:9:13-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b39184e5b83e3ce2a9d7be35d0e172a5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
