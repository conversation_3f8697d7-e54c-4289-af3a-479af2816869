<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_350"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >
    <LinearLayout
        android:layout_width="@dimen/dp_300"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_margin="@dimen/dp_10"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="扫描到的设备(单击打开)"
                android:textSize="@dimen/sp_15"
                android:textColor="@color/black"
                android:gravity="center"
                android:layout_gravity="center"
                />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp_10"
            >

        </androidx.recyclerview.widget.RecyclerView>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            >
            <Button
                android:id="@+id/cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="关闭"
                android:textColor="@color/white"
                android:background="@drawable/btn_round"
                android:textSize="@dimen/sp_15"
                />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
