// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogGpioItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatCheckBox cbEnable;

  @NonNull
  public final AppCompatCheckBox cbVal;

  @NonNull
  public final AppCompatRadioButton rbIn;

  @NonNull
  public final AppCompatRadioButton rbOut;

  @NonNull
  public final TextView tvLabel;

  private DialogGpioItemBinding(@NonNull LinearLayout rootView, @NonNull AppCompatCheckBox cbEnable,
      @NonNull AppCompatCheckBox cbVal, @NonNull AppCompatRadioButton rbIn,
      @NonNull AppCompatRadioButton rbOut, @NonNull TextView tvLabel) {
    this.rootView = rootView;
    this.cbEnable = cbEnable;
    this.cbVal = cbVal;
    this.rbIn = rbIn;
    this.rbOut = rbOut;
    this.tvLabel = tvLabel;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogGpioItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogGpioItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_gpio_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogGpioItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cbEnable;
      AppCompatCheckBox cbEnable = ViewBindings.findChildViewById(rootView, id);
      if (cbEnable == null) {
        break missingId;
      }

      id = R.id.cbVal;
      AppCompatCheckBox cbVal = ViewBindings.findChildViewById(rootView, id);
      if (cbVal == null) {
        break missingId;
      }

      id = R.id.rbIn;
      AppCompatRadioButton rbIn = ViewBindings.findChildViewById(rootView, id);
      if (rbIn == null) {
        break missingId;
      }

      id = R.id.rbOut;
      AppCompatRadioButton rbOut = ViewBindings.findChildViewById(rootView, id);
      if (rbOut == null) {
        break missingId;
      }

      id = R.id.tvLabel;
      TextView tvLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvLabel == null) {
        break missingId;
      }

      return new DialogGpioItemBinding((LinearLayout) rootView, cbEnable, cbVal, rbIn, rbOut,
          tvLabel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
