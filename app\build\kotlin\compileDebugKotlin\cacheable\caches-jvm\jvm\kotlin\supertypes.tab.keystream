2com.petterp.floatingx.app.service.LauncherReceiver1com.petterp.floatingx.app.service.LauncherService0com.petterp.floatingx.app.simple.FxAnimationImpl8com.petterp.floatingx.app.simple.FxConfigStorageToSpImpl%com.dp.floating.CustomJavaApplication-com.dp.floating.ui.DeviceAdapter.MyViewHolder$com.dp.floating.ui.SerialListAdapter.com.dp.floating.databinding.CustomToastBinding-com.dp.floating.ui.ModemErrorEntity.ErrorType)com.dp.floating.ui.UsbDeviceDialogAdapter6com.dp.floating.ui.UsbDeviceDialogAdapter.MyViewHolder1com.dp.floating.databinding.DialogGpioItemBindingcom.dp.floating.MainActivity/com.dp.floating.databinding.DialogSerialBinding-com.dp.floating.databinding.DeviceItemBinding7com.dp.floating.databinding.DialogDevicelistItemBinding-com.dp.floating.databinding.SerialItemBinding%com.dp.floating.ui.SerialConfigDialog/com.dp.floating.databinding.ActivityMainBinding-com.dp.floating.databinding.DialogGpioBinding#com.dp.floating.ui.DeviceListDialog3com.dp.floating.databinding.DialogDevicelistBinding4com.dp.floating.databinding.MySpinnerTextviewBinding com.dp.floating.ui.DeviceAdapter1com.dp.floating.ui.SerialListAdapter.MyViewHolder!com.dp.floating.ui.CustomTextViewcom.dp.floating.WebServer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       