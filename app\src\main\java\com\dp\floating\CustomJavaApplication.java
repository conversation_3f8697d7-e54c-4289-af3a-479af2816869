package com.dp.floating;


import android.app.Activity;
import android.app.Application;
import android.content.Context;

import com.petterp.floatingx.FloatingX;

import com.petterp.floatingx.app.simple.FxAnimationImpl;
import com.petterp.floatingx.app.simple.FxConfigStorageToSpImpl;
import com.petterp.floatingx.assist.FxDisplayMode;
import com.petterp.floatingx.assist.FxScopeType;
import com.petterp.floatingx.assist.helper.FxAppHelper;
import com.petterp.floatingx.assist.helper.FxScopeHelper;

import cn.wch.uartlib.WCHUARTManager;
import cn.wch.uartlib.chip.type.ChipType2;

/**
 * java 中的配置示例
 *
 * <AUTHOR>
 */
public class CustomJavaApplication extends Application {

    private static Application application;
    @Override
    public void onCreate() {
        super.onCreate();
        application=this;
        WCHUARTManager.getInstance().init(this);
        //WCHUARTManager.setReadTimeout(0);
        //WCHUARTManager.addNewHardware(0x1a86,0x7523);
        WCHUARTManager.setDebug(true);
        //增加0x1a86:0x55D4 并且强制指定类型为CH9102X
//        WCHUARTManager.addNewHardwareAndChipType(0x1a86,0x55D3, ChipType2.CHIP_CH343GP);
        //解决GPIO不识别问题
        WCHUARTManager.addNewHardwareAndChipType(0x1a86,0x55D8, ChipType2.CHIP_CH9101UH);
//        WCHUARTManager.addNewHardwareAndChipType(0x1a86,0x55D4,ChipType2.CHIP_CH9102F);

        FxAppHelper helper = FxAppHelper.builder()
                .setContext(this)
                .setLayout(R.layout.item_floating)
                // 设置启用日志,tag可以自定义，最终显示为FloatingX-xxx
                .setEnableLog(true, "自定义的tag")
                .setScopeType(FxScopeType.SYSTEM_AUTO)

                //1. 是否允许全局显示悬浮窗,默认true
                .setEnableAllInstall(true)
                //2. 禁止插入Activity的页面, setEnableAllBlackClass(true)时,此方法生效
//                .addInstallBlackClass(BlackActivity.class)
                //3. 允许插入Activity的页面, setEnableAllBlackClass(false)时,此方法生效
//                .addInstallWhiteClass(MainActivity.class, ScopeActivity.class)

                // 设置启用边缘吸附
                .setEnableEdgeAdsorption(false)
                // 设置边缘偏移量
                .setEdgeOffset(0)
                // 设置启用悬浮窗可屏幕外回弹
                .setEnableScrollOutsideScreen(false)
                // 设置辅助方向辅助
                // 设置点击事件
//                .setOnClickListener()
                // 设置view-lifecycle监听
//            setViewLifecycle()
                // 设置启用动画
                .setEnableAnimation(true)
                // 设置启用动画实现
                .setAnimationImpl(new FxAnimationImpl())
                // 设置方向保存impl
                .setSaveDirectionImpl(new FxConfigStorageToSpImpl(this))

                // 设置底部偏移量
                .setBottomBorderMargin(0)
                // 设置顶部偏移量
//            setTopBorderMargin(100f)
                // 设置左侧偏移量
                .setLeftBorderMargin(0)
                // 设置右侧偏移量
                .setRightBorderMargin(0)
                // 设置浮窗展示类型，默认可移动可点击，无需配置
                .setDisplayMode(FxDisplayMode.Normal)
                //启用悬浮窗,即默认会插入到允许的activity中
                // 启用悬浮窗,相当于一个标记,会自动插入允许的activity中
                .build();
//        FloatingX.install(helper).show();
    }

    /**
     * 创建一个局部悬浮窗
     */
    public void createScopeFxSimple(Activity activity) {
        FxScopeHelper.builder()
                .setLayout(R.layout.item_floating)
                .build()
                .toControl(activity);
    }

    public static Context getContext(){
        return application;
    }

}
