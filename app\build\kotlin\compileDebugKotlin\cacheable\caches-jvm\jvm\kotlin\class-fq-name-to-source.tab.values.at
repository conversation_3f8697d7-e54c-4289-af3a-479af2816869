/ Header Record For PersistentHashMapValueStorage< ;app/src/main/java/com/dp/floating/service/FxSystemSimple.kt> =app/src/main/java/com/dp/floating/service/LauncherReceiver.kt> =app/src/main/java/com/dp/floating/service/LauncherReceiver.kt= <app/src/main/java/com/dp/floating/service/LauncherService.kt< ;app/src/main/java/com/dp/floating/simple/FxAnimationImpl.ktD Capp/src/main/java/com/dp/floating/simple/FxConfigStorageToSpImpl.ktD Capp/src/main/java/com/dp/floating/simple/FxConfigStorageToSpImpl.kt= <app/src/main/java/com/dp/floating/CustomJavaApplication.java= <app/src/main/java/com/dp/floating/CustomJavaApplication.java= <app/src/main/java/com/dp/floating/CustomJavaApplication.java< ;app/src/main/java/com/dp/floating/service/FxSystemSimple.kt8 7app/src/main/java/com/dp/floating/ui/DeviceAdapter.java< ;app/src/main/java/com/dp/floating/ui/SerialListAdapter.java; :app/src/main/java/com/dp/floating/ui/DeviceListDialog.java8 7app/src/main/java/com/dp/floating/ui/DeviceAdapter.javau tapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/CustomToastBinding.java5 4app/src/main/java/com/dp/floating/tools/LogUtil.java; :app/src/main/java/com/dp/floating/ui/ModemErrorEntity.javaA @app/src/main/java/com/dp/floating/ui/UsbDeviceDialogAdapter.javaA @app/src/main/java/com/dp/floating/ui/UsbDeviceDialogAdapter.java; :app/src/main/java/com/dp/floating/ui/ModemErrorEntity.java6 5app/src/main/java/com/dp/floating/ui/ModemEntity.java= <app/src/main/java/com/dp/floating/ui/SerialConfigDialog.javax wapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DialogGpioItemBinding.java8 7app/src/main/java/com/dp/floating/tools/FormatUtil.java7 6app/src/main/java/com/dp/floating/ui/DeviceEntity.java4 3app/src/main/java/com/dp/floating/MainActivity.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DialogSerialBinding.javat sapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DeviceItemBinding.javaA @app/src/main/java/com/dp/floating/ui/UsbDeviceDialogAdapter.java~ }app/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DialogDevicelistItemBinding.javat sapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/SerialItemBinding.java= <app/src/main/java/com/dp/floating/ui/SerialConfigDialog.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/ActivityMainBinding.java9 8app/src/main/java/com/dp/floating/ui/SerialBaudBean.javat sapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DialogGpioBinding.java= <app/src/main/java/com/dp/floating/CustomJavaApplication.java; :app/src/main/java/com/dp/floating/ui/DeviceListDialog.java7 6app/src/main/java/com/dp/floating/ui/SerialEntity.java7 6app/src/main/java/com/dp/floating/tools/ToastUtil.javaz yapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/DialogDevicelistBinding.java{ zapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/MySpinnerTextviewBinding.java8 7app/src/main/java/com/dp/floating/ui/DeviceAdapter.java< ;app/src/main/java/com/dp/floating/ui/SerialListAdapter.java9 8app/src/main/java/com/dp/floating/ui/CustomTextView.java4 3app/src/main/java/com/dp/floating/MainActivity.javat sapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/SerialItemBinding.java< ;app/src/main/java/com/dp/floating/ui/SerialListAdapter.java< ;app/src/main/java/com/dp/floating/ui/SerialListAdapter.java= <app/src/main/java/com/dp/floating/CustomJavaApplication.java4 3app/src/main/java/com/dp/floating/MainActivity.java4 3app/src/main/java/com/dp/floating/MainActivity.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/ActivityMainBinding.java4 3app/src/main/java/com/dp/floating/MainActivity.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/ActivityMainBinding.java4 3app/src/main/java/com/dp/floating/MainActivity.java4 3app/src/main/java/com/dp/floating/MainActivity.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/ActivityMainBinding.java4 3app/src/main/java/com/dp/floating/MainActivity.java1 0app/src/main/java/com/dp/floating/WebServer.java4 3app/src/main/java/com/dp/floating/MainActivity.javav uapp/build/generated/data_binding_base_class_source_out/debug/out/com/dp/floating/databinding/ActivityMainBinding.java1 0app/src/main/java/com/dp/floating/WebServer.java1 0app/src/main/java/com/dp/floating/WebServer.java4 3app/src/main/java/com/dp/floating/MainActivity.java1 0app/src/main/java/com/dp/floating/WebServer.java4 3app/src/main/java/com/dp/floating/MainActivity.java1 0app/src/main/java/com/dp/floating/WebServer.java1 0app/src/main/java/com/dp/floating/WebServer.java1 0app/src/main/java/com/dp/floating/WebServer.java4 3app/src/main/java/com/dp/floating/MainActivity.java1 0app/src/main/java/com/dp/floating/WebServer.java4 3app/src/main/java/com/dp/floating/MainActivity.java4 3app/src/main/java/com/dp/floating/MainActivity.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.java1 0app/src/main/java/com/dp/floating/WebServer.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.javaA @app/src/main/java/com/dp/floating/service/BackgroundService.java