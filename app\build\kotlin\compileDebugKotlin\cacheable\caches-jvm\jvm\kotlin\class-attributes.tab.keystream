&com.dp.floating.service.FxSystemSimple2com.petterp.floatingx.app.service.LauncherReceiver<com.petterp.floatingx.app.service.LauncherReceiver.Companion1com.petterp.floatingx.app.service.LauncherService0com.petterp.floatingx.app.simple.FxAnimationImpl8com.petterp.floatingx.app.simple.FxConfigStorageToSpImplBcom.petterp.floatingx.app.simple.FxConfigStorageToSpImpl.Companion%com.dp.floating.CustomJavaApplication-com.dp.floating.ui.DeviceAdapter.MyViewHolder$com.dp.floating.ui.SerialListAdapter3com.dp.floating.ui.DeviceListDialog.OnClickListener1com.dp.floating.ui.DeviceAdapter.OnActionListener.com.dp.floating.databinding.CustomToastBindingcom.dp.floating.tools.LogUtil-com.dp.floating.ui.ModemErrorEntity.ErrorType)com.dp.floating.ui.UsbDeviceDialogAdapter6com.dp.floating.ui.UsbDeviceDialogAdapter.MyViewHolder#com.dp.floating.ui.ModemErrorEntitycom.dp.floating.ui.ModemEntity5com.dp.floating.ui.SerialConfigDialog.onClickListener1com.dp.floating.databinding.DialogGpioItemBinding com.dp.floating.tools.FormatUtilcom.dp.floating.ui.DeviceEntitycom.dp.floating.MainActivity/com.dp.floating.databinding.DialogSerialBinding-com.dp.floating.databinding.DeviceItemBinding9com.dp.floating.ui.UsbDeviceDialogAdapter.OnClickListener7com.dp.floating.databinding.DialogDevicelistItemBinding-com.dp.floating.databinding.SerialItemBinding%com.dp.floating.ui.SerialConfigDialog/com.dp.floating.databinding.ActivityMainBinding!com.dp.floating.ui.SerialBaudBean-com.dp.floating.databinding.DialogGpioBinding#com.dp.floating.ui.DeviceListDialogcom.dp.floating.ui.SerialEntitycom.dp.floating.tools.ToastUtil3com.dp.floating.databinding.DialogDevicelistBinding4com.dp.floating.databinding.MySpinnerTextviewBinding com.dp.floating.ui.DeviceAdapter1com.dp.floating.ui.SerialListAdapter.MyViewHolder!com.dp.floating.ui.CustomTextViewcom.dp.floating.WebServer5com.dp.floating.service.BackgroundService.LocalBinder)com.dp.floating.service.BackgroundService                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      