package com.dp.floating;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.hardware.usb.UsbDevice;
import android.os.Bundle;

import com.dp.floating.tools.FormatUtil;
import com.dp.floating.tools.LogUtil;
import com.dp.floating.ui.DeviceAdapter;
import com.dp.floating.ui.DeviceEntity;
import com.dp.floating.ui.DeviceListDialog;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.navigation.ui.AppBarConfiguration;

import com.dp.floating.databinding.ActivityMainBinding;
import com.dp.floating.ui.ModemErrorEntity;
import com.dp.floating.ui.SerialBaudBean;
import com.dp.floating.ui.SerialEntity;

import android.view.Menu;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

import cn.wch.uartlib.WCHUARTManager;
import cn.wch.uartlib.base.error.SerialErrorType;
import cn.wch.uartlib.callback.IDataCallback;
import cn.wch.uartlib.callback.IModemStatus;
import cn.wch.uartlib.callback.IUsbStateChange;
import cn.wch.uartlib.chip.type.ChipType2;
import cn.wch.uartlib.exception.ChipException;
import cn.wch.uartlib.exception.NoPermissionException;
import cn.wch.uartlib.exception.UartLibException;

public class MainActivity extends AppCompatActivity {

    private AppBarConfiguration appBarConfiguration;
    private ActivityMainBinding binding;

    final Set<UsbDevice> devices= Collections.synchronizedSet(new HashSet<UsbDevice>());

    private static boolean FILE_TEST=false;

    //该Map的key是每个设备的串口，value是其对应的保存数据的文件的fileStream
    private HashMap<String, FileOutputStream> fileOutputStreamMap=new HashMap<>();

    DeviceAdapter deviceAdapter;
    Thread readThread;
    boolean flag=false;

    private Context context;


    private Button button,sendBtn,webServerBtn;

    private EditText edittext;


    private final boolean useReadThread=false;

    SerialEntity serialEntity;

    //保存各个串口的接收计数
    HashMap<String, Integer> readCountMap=new HashMap<>();

    // Web服务器实例
    private WebServer webServer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.context=this;
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setSupportActionBar(binding.toolbar);

        button = findViewById(R.id.button);
        sendBtn = findViewById(R.id.send);
        webServerBtn = findViewById(R.id.webServerBtn);

        connectDevice();
        deviceAdapter =new DeviceAdapter(this);
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                enumDevice();
            }
        });
        sendBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                String a = "push_bat 0 0 \n \n";
                send(a);
            }
        });

        // Web服务器按钮点击事件
        webServerBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleWebServer();
            }
        });

        if(!UsbFeatureSupported()){
            showToast("系统不支持USB Host功能");
            System.exit(0);
            return;
        }


        //监测USB插拔状态
        monitorUSBState();
        //动态申请权限
        if(ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)!= PackageManager.PERMISSION_GRANTED){
            ActivityCompat.requestPermissions(this,new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},111);
        }


    }

    void connectDevice(){
        try {
            //枚举符合要求的设备
            ArrayList<UsbDevice> usbDeviceArrayList = WCHUARTManager.getInstance().enumDevice();
            if(usbDeviceArrayList.size()==0){
                showToast("no matched devices");
                return;
            }
            open(usbDeviceArrayList.get(0));
        } catch (Exception e) {
            Log.d("dp",e.getMessage());
        }
    }
    /**
     * 枚举当前所有符合要求的设备，显示设备列表
     */
    void enumDevice(){
        try {
            //枚举符合要求的设备
            ArrayList<UsbDevice> usbDeviceArrayList = WCHUARTManager.getInstance().enumDevice();
            if(usbDeviceArrayList.size()==0){
                showToast("no matched devices");
                return;
            }
            //显示设备列表dialog
            DeviceListDialog deviceListDialog=DeviceListDialog.newInstance(usbDeviceArrayList);
            deviceListDialog.setCancelable(false);
            deviceListDialog.show(getSupportFragmentManager(),DeviceListDialog.class.getName());
            deviceListDialog.setOnClickListener(new DeviceListDialog.OnClickListener() {
                @Override
                public void onClick(UsbDevice usbDevice) {
                    //选择了某一个设备打开
                    open(usbDevice);
                }
            });
        } catch (Exception e) {
            Log.d("dp",e.getMessage());
        }
    }

    boolean setSerialParameter(UsbDevice usbDevice, int serialNumber, SerialBaudBean baudBean){
        try {
            boolean b = WCHUARTManager.getInstance().setSerialParameter(usbDevice, serialNumber,
                    baudBean.getBaud(), baudBean.getData(), baudBean.getStop(), baudBean.getParity(),baudBean.isFlow());
            return b;
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
        return false;
    }
    public void send(String msg){
        if(TextUtils.isEmpty(msg)){
            showToast("发送内容为空");
            return;
        }
        byte[] bytes = null;
//        if(holder.scWrite.isChecked()){
//            if(!msg.matches("([0-9|a-f|A-F]{2})*")){
//                showToast("发送内容不符合HEX规范");
//                return;
//            }
//            bytes= FormatUtil.hexStringToBytes(msg);
//        }else {
//            bytes = msg.getBytes(StandardCharsets.UTF_8);
//        }

        bytes = msg.getBytes(StandardCharsets.UTF_8);
        int ret = writeData(serialEntity.getUsbDevice(), serialEntity.getSerialNumber(), bytes, bytes.length);
        if(ret>0){
//            //更新发送计数
//            int writeCount = getWriteCount(serialEntity.getSerialNumber());
//            writeCount+=ret;
//            setWriteCount(serialEntity.getSerialNumber(),writeCount);
//            holder.writeCount.setText(String.format(Locale.getDefault(),"发送计数：%d字节",writeCount));
            showToast("发送成功");
        }else {
            showToast("发送失败");
        }
    }

    int writeData(UsbDevice usbDevice,int serialNumber,@NonNull byte[] data,int length){
        try {
            int write = WCHUARTManager.getInstance().syncWriteData(usbDevice, serialNumber, data,length,2000);
            return write;
        } catch (Exception e) {
            LogUtil.d(e.getMessage());

        }
        return -2;
    }



    void open(@NonNull UsbDevice usbDevice){
        if(WCHUARTManager.getInstance().isConnected(usbDevice)){
            showToast("当前设备已经打开");
            return;
        }
        try {
            boolean b = WCHUARTManager.getInstance().openDevice(usbDevice);
            if(b){
                //打开成功
                //更新显示的ui
                update(usbDevice);

                serialEntity = new SerialEntity(usbDevice,0);


                SerialBaudBean serialBaudBean = new SerialBaudBean();
                serialBaudBean.setBaud(115200);
                serialBaudBean.setData(8);
                serialBaudBean.setStop(1);
                serialBaudBean.setParity(0);

                setSerialParameter(serialEntity.getUsbDevice(),serialEntity.getSerialNumber(),serialBaudBean );




                //初始化接收计数
                int serialCount = 0;
                try {
                    serialCount = WCHUARTManager.getInstance().getSerialCount(usbDevice);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                for (int i = 0; i < serialCount; i++) {
                    readCountMap.put(FormatUtil.getSerialKey(usbDevice,i),0);
                }
                //将该设备添加至已打开设备列表,在读线程ReadThread中,将会读取该设备的每个串口数据
                addToReadDeviceSet(usbDevice);
                //用作文件对比测试,在打开每个设备时，对每个串口新建对应的保存数据的文件

                registerModemStatusCallback(usbDevice);
                if(!useReadThread){
                    registerDataCallback(usbDevice);
                }
            }else {
                showToast("打开失败");
            }
        } catch (ChipException e) {
            LogUtil.d(e.getMessage());
        } catch (NoPermissionException e) {
            //没有权限打开该设备
            //申请权限
            showToast("没有权限打开该设备");
            requestPermission(usbDevice);
        } catch (UartLibException e) {
            e.printStackTrace();
        }
    }
    private void registerDataCallback(UsbDevice usbDevice){
        try {
            WCHUARTManager.getInstance().registerDataCallback(usbDevice, new IDataCallback() {
                @Override
                public void onData(int serialNumber, byte[] buffer, int length) {
                    //LogUtil.d(String.format(Locale.getDefault(),"serial %d receive data %d:%s", serialNumber,length, FormatUtil.bytesToHexString(buffer, length)));
                    //1.注意回调的执行线程与调用回调方法的线程属于同一线程
                    //2.此处所在的线程将是线程池中多个端点的读取线程，可打印线程id查看
                    //3.buffer是底层数组，如果此处将其传给其他线程使用，例如通过runOnUiThread显示数据在界面上,
                    //涉及到线程切换需要一定时间，buffer可能被读到的新数据覆盖，可以新建一个临时数组保存数据
                    LogUtil.d("Application onData");
                    byte[] data=new byte[length];
                    System.arraycopy(buffer,0,data,0,data.length);



                }
            });
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
    }

    private void registerModemStatusCallback(UsbDevice usbDevice){
        try {
            WCHUARTManager.getInstance().registerModemStatusCallback(usbDevice, new IModemStatus() {
                @Override
                public void onStatusChanged(int serialNumber, boolean isDCDRaised, boolean isDSRRaised, boolean isCTSRaised, boolean isRINGRaised) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            deviceAdapter.updateDeviceModemStatus(usbDevice,serialNumber,isDCDRaised,isDSRRaised,isCTSRaised,isRINGRaised);
                        }
                    });
                }

                @Override
                public void onOverrunError(int serialNumber) {
                    try {
                        int count=WCHUARTManager.getInstance().querySerialErrorCount(usbDevice,serialNumber, SerialErrorType.OVERRUN);
                        LogUtil.d("overrun error: "+count);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            deviceAdapter.updateDeviceModemErrorStatus(usbDevice,new ModemErrorEntity(serialNumber, ModemErrorEntity.ErrorType.OVERRUN));

                        }
                    });

                }

                @Override
                public void onParityError(int serialNumber) {
                    try {
                        int count=WCHUARTManager.getInstance().querySerialErrorCount(usbDevice,serialNumber, SerialErrorType.PARITY);
                        LogUtil.d("parity error: "+count);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            LogUtil.d("parity error!");
                            deviceAdapter.updateDeviceModemErrorStatus(usbDevice,new ModemErrorEntity(serialNumber, ModemErrorEntity.ErrorType.PARITY));

                        }
                    });
                }

                @Override
                public void onFrameError(int serialNumber) {
                    try {
                        int count=WCHUARTManager.getInstance().querySerialErrorCount(usbDevice,serialNumber, SerialErrorType.FRAME);
                        LogUtil.d("frame error: "+count);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            LogUtil.d("frame error!");
                            deviceAdapter.updateDeviceModemErrorStatus(usbDevice,new ModemErrorEntity(serialNumber, ModemErrorEntity.ErrorType.FRAME));

                        }
                    });
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * //recyclerView更新UI
     * @param usbDevice
     */
    void update(UsbDevice usbDevice){
        //根据vid/pid获取芯片类型
        ChipType2 chipType = null;
        try {
            chipType = WCHUARTManager.getInstance().getChipType(usbDevice);
            //获取芯片串口数目,为负则代表出错
            int serialCount = WCHUARTManager.getInstance().getSerialCount(usbDevice);
            //构建recyclerView所绑定的数据,添加设备
            ArrayList<SerialEntity> serialEntities=new ArrayList<>();
            for (int i = 0; i < serialCount; i++) {
                SerialEntity serialEntity=new SerialEntity(usbDevice,i);
                serialEntities.add(serialEntity);
            }
            DeviceEntity deviceEntity=new DeviceEntity(usbDevice,chipType.getDescription(),serialEntities);
            if(deviceAdapter.hasExist(deviceEntity)){
                //已经显示
                showToast("该设备已经存在");
            }else {
                deviceAdapter.addDevice(deviceEntity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


//    void openGPIODialog(){
//        //simply,select first device
//        UsbDevice device=null;
//        Iterator<UsbDevice> iterator = devices.iterator();
//        while (iterator.hasNext()){
//            device = iterator.next();
//        }
//        if(device!=null){
//            GPIODialog dialog=GPIODialog.newInstance(device);
//            dialog.setCancelable(false);
//            dialog.show(getSupportFragmentManager(),GPIODialog.class.getName());
//
//        }
//
//    }



    /**
     * 申请读写权限
     * @param usbDevice
     */
    private void requestPermission(@NonNull UsbDevice usbDevice){
        try {
            WCHUARTManager.getInstance().requestPermission(this,usbDevice);
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
    }


    private void showToast(String message){
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //ToastUtil.create(context,message).show();
                Toast.makeText(context,message,Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        //停止读线程
        if(useReadThread){
            stopReadThread();
        }

        //关闭所有连接设备
        closeAll();
        //释放资源
        WCHUARTManager.getInstance().close(CustomJavaApplication.getContext());

        // 停止Web服务器
        if (webServer != null) {
            webServer.stopServer();
            webServer = null;
        }

        Log.d("dp","onDestroy");
    }

    void closeAll(){
        ArrayList<UsbDevice> usbDeviceArrayList = null;
        try {
            usbDeviceArrayList = WCHUARTManager.getInstance().enumDevice();
            for (UsbDevice usbDevice : usbDeviceArrayList) {
                if(WCHUARTManager.getInstance().isConnected(usbDevice)){
                    WCHUARTManager.getInstance().disconnect(usbDevice);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void stopReadThread(){
        if(readThread!=null && readThread.isAlive()){
            flag=false;
        }
    }

    /**
     * 系统是否支持USB Host功能
     *
     * @return true:系统支持USB Host false:系统不支持USB Host
     */
    public boolean UsbFeatureSupported() {
        boolean bool = this.getPackageManager().hasSystemFeature(
                "android.hardware.usb.host");
        return bool;
    }

    private void addToReadDeviceSet(@NonNull UsbDevice usbDevice){
        synchronized (devices){
            devices.add(usbDevice);
        }

    }


    private void removeReadDataDevice(@NonNull UsbDevice usbDevice){
        synchronized (devices){
            devices.remove(usbDevice);
        }
    }
    /**
     * 监测USB的状态
     */
    private void monitorUSBState(){
        WCHUARTManager.getInstance().setUsbStateListener(new IUsbStateChange() {
            @Override
            public void usbDeviceDetach(UsbDevice device) {
                //设备移除
                removeReadDataDevice(device);

                if(FILE_TEST){
                    cancelDeviceLinks(device);
                }
            }

            @Override
            public void usbDeviceAttach(UsbDevice device) {
                //设备插入
            }

            @Override
            public void usbDevicePermission(UsbDevice device, boolean result) {
                //请求打开设备权限结果
            }
        });
    }


    private void cancelDeviceLinks(UsbDevice usbDevice){
        if(fileOutputStreamMap==null){
            return;
        }
        for (int i = 0; i < 8; i++) {
            FileOutputStream fileOutputStream = fileOutputStreamMap.get(FormatUtil.getSerialKey(usbDevice, i));
            if(fileOutputStream==null){
                continue;
            }
            Log.d("dp","close file"+usbDevice.getDeviceName()+" "+i);
            try {
                fileOutputStream.flush();
                fileOutputStream.close();
                fileOutputStream=null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    // Web服务器管理方法
    private void toggleWebServer() {
        if (webServer == null || !webServer.isAlive()) {
            startWebServer();
        } else {
            stopWebServer();
        }
    }

    private void startWebServer() {
        try {
            webServer = new WebServer(this);
            webServer.startServer();
            webServerBtn.setText("停止Web服务器");
            showToast("Web服务器已启动，端口: 8080\n请查看日志获取访问地址");
            Log.i("MainActivity", "Web服务器启动成功");
        } catch (Exception e) {
            Log.e("MainActivity", "启动Web服务器失败", e);
            showToast("启动Web服务器失败: " + e.getMessage());
        }
    }

    private void stopWebServer() {
        if (webServer != null) {
            webServer.stopServer();
            webServer = null;
            webServerBtn.setText("启动Web服务器");
            showToast("Web服务器已停止");
            Log.i("MainActivity", "Web服务器已停止");
        }
    }

}