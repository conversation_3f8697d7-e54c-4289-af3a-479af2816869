package com.dp.floating;

import android.Manifest;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.hardware.usb.UsbDevice;
import android.os.Bundle;
import android.os.IBinder;

import com.dp.floating.tools.FormatUtil;
import com.dp.floating.tools.LogUtil;
import com.dp.floating.ui.DeviceAdapter;
import com.dp.floating.ui.DeviceEntity;
import com.dp.floating.ui.DeviceListDialog;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.navigation.ui.AppBarConfiguration;

import com.dp.floating.databinding.ActivityMainBinding;
import com.dp.floating.service.BackgroundService;
import com.dp.floating.ui.ModemErrorEntity;
import com.dp.floating.ui.SerialBaudBean;
import com.dp.floating.ui.SerialEntity;

import android.view.Menu;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

import cn.wch.uartlib.WCHUARTManager;
import cn.wch.uartlib.base.error.SerialErrorType;
import cn.wch.uartlib.callback.IDataCallback;
import cn.wch.uartlib.callback.IModemStatus;
import cn.wch.uartlib.callback.IUsbStateChange;
import cn.wch.uartlib.chip.type.ChipType2;
import cn.wch.uartlib.exception.ChipException;
import cn.wch.uartlib.exception.NoPermissionException;
import cn.wch.uartlib.exception.UartLibException;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;
    private TextView textview;
    private Context context;
    private Button button, sendBtn, webServerBtn;

    // 服务相关
    private BackgroundService backgroundService;
    private boolean serviceBound = false;

    // UI相关
    DeviceAdapter deviceAdapter;

    // 服务连接回调
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder service) {
            BackgroundService.LocalBinder binder = (BackgroundService.LocalBinder) service;
            backgroundService = binder.getService();
            serviceBound = true;
            Log.d("MainActivity", "服务已连接");
            updateUI();
        }

        @Override
        public void onServiceDisconnected(ComponentName arg0) {
            serviceBound = false;
            backgroundService = null;
            Log.d("MainActivity", "服务已断开");
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.context = this;
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setSupportActionBar(binding.toolbar);

        // 初始化UI组件
        button = findViewById(R.id.button);
        sendBtn = findViewById(R.id.send);
        webServerBtn = findViewById(R.id.webServerBtn);
        textview = findViewById(R.id.textview);

        deviceAdapter = new DeviceAdapter(this);

        // 设置按钮点击事件
        setupButtonListeners();

        // 动态申请权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 111);
        }

        // 启动并绑定后台服务
        startAndBindBackgroundService();
    }

    private void setupButtonListeners() {
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                enumDevice();
            }
        });

        sendBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String data = "push_bat 0 0 \n \n";
                if (serviceBound && backgroundService != null) {
                    backgroundService.send(data);
                    showToast("数据已发送");
                } else {
                    showToast("服务未连接");
                }
            }
        });

        webServerBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleWebServer();
            }
        });
    }

    private void startAndBindBackgroundService() {
        // 启动后台服务
        Intent serviceIntent = new Intent(this, BackgroundService.class);
        startForegroundService(serviceIntent);

        // 绑定服务
        bindService(serviceIntent, serviceConnection, Context.BIND_AUTO_CREATE);
    }

    private void updateUI() {
        if (serviceBound && backgroundService != null) {
            // 更新Web服务器按钮状态
            if (backgroundService.isWebServerRunning()) {
                webServerBtn.setText("停止Web服务器");
            } else {
                webServerBtn.setText("启动Web服务器");
            }

            // 更新状态显示
            String status = backgroundService.getServiceStatus();
            if (textview != null) {
                textview.setText(status);
            }
        }
    }

    /**
     * 枚举当前所有符合要求的设备，显示设备列表
     */
    void enumDevice() {
        if (!serviceBound || backgroundService == null) {
            showToast("服务未连接");
            return;
        }

        try {
            ArrayList<UsbDevice> usbDeviceArrayList = backgroundService.enumDevice();
            if (usbDeviceArrayList.size() == 0) {
                showToast("no matched devices");
                return;
            }

            // 显示设备列表dialog
            DeviceListDialog deviceListDialog = DeviceListDialog.newInstance(usbDeviceArrayList);
            deviceListDialog.setCancelable(false);
            deviceListDialog.show(getSupportFragmentManager(), DeviceListDialog.class.getName());
            deviceListDialog.setOnClickListener(new DeviceListDialog.OnClickListener() {
                @Override
                public void onClick(UsbDevice usbDevice) {
                    // 选择了某一个设备打开
                    if (serviceBound && backgroundService != null) {
                        backgroundService.open(usbDevice);
                        showToast("正在打开设备: " + usbDevice.getDeviceName());
                        updateUI(); // 更新UI状态
                    }
                }
            });
        } catch (Exception e) {
            Log.d("MainActivity", e.getMessage());
            showToast("枚举设备失败: " + e.getMessage());
        }
    }

    boolean setSerialParameter(UsbDevice usbDevice, int serialNumber, SerialBaudBean baudBean){
        try {
            boolean b = WCHUARTManager.getInstance().setSerialParameter(usbDevice, serialNumber,
                    baudBean.getBaud(), baudBean.getData(), baudBean.getStop(), baudBean.getParity(),baudBean.isFlow());
            return b;
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
        return false;
    }
    // 以下方法已移至BackgroundService：
    // - open()
    // - registerDataCallback()
    // - registerModemStatusCallback()
    // - update()
    // - requestPermission()
    // - closeAll()
    // - stopReadThread()
    // - UsbFeatureSupported()
    // - addToReadDeviceSet()
    // - removeReadDataDevice()
    // - monitorUSBState()
    // - cancelDeviceLinks()

    private void showToast(String message) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 解绑服务
        if (serviceBound) {
            unbindService(serviceConnection);
            serviceBound = false;
        }
        Log.d("MainActivity", "Activity已销毁");
    }

    // Web服务器管理方法
    private void toggleWebServer() {
        if (!serviceBound || backgroundService == null) {
            showToast("服务未连接");
            return;
        }

        if (backgroundService.isWebServerRunning()) {
            backgroundService.stopWebServer();
            webServerBtn.setText("启动Web服务器");
            showToast("Web服务器已停止");
        } else {
            backgroundService.startWebServer();
            webServerBtn.setText("停止Web服务器");
            showToast("Web服务器已启动");
        }

        // 更新UI状态
        updateUI();
    }

    @Override
    protected void onStart() {
        super.onStart();
        // 重新绑定服务（如果需要）
        if (!serviceBound) {
            Intent intent = new Intent(this, BackgroundService.class);
            bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // 注意：不要在onStop中解绑服务，因为我们希望服务在后台继续运行
    }

}