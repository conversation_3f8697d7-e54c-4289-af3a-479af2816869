R_DEF: Internal format may change without notice
local
array baud
array data
array parity
array stop
color black
color blue
color grey
color grey_dark
color grey_light
color purple_200
color purple_500
color purple_700
color red
color teal_200
color teal_700
color white
dimen dp_10
dimen dp_100
dimen dp_120
dimen dp_15
dimen dp_20
dimen dp_250
dimen dp_30
dimen dp_300
dimen dp_35
dimen dp_350
dimen dp_40
dimen dp_5
dimen dp_50
dimen dp_70
dimen dp_90
dimen fab_margin
dimen sp_10
dimen sp_11
dimen sp_12
dimen sp_13
dimen sp_14
dimen sp_15
dimen sp_16
dimen sp_17
drawable aaa
drawable bg_spinner
drawable btn_round
drawable ic_launcher
drawable ll_border
drawable ll_round
drawable ll_round_black
id FirstFragment
id SecondFragment
id action_FirstFragment_to_SecondFragment
id action_SecondFragment_to_FirstFragment
id action_settings
id btnClose
id button
id cancel
id cardItemFx
id cbBreak
id cbCTS
id cbDCD
id cbDSR
id cbDTR
id cbEnable
id cbFrame
id cbOverrun
id cbParity
id cbRTS
id cbRing
id cbVal
id fab
id id
id list
id name
id nav_graph
id queryErrorStatus
id rbIn
id rbOut
id rvGPIO
id rvSerial
id scFLow
id scWrite
id send
id send_data
id set
id spinner_baud
id spinner_data
id spinner_parity
id spinner_stop
id text
id textview
id toolbar
id tvClearWrite
id tvConfigGPIO
id tvDes
id tvDescription
id tvGetAllGPIO
id tvGetGPIOVal
id tvItemFx
id tvLabel
id tvSerialConfig
id tvSerialDescription
id tvSerialInfo
id tvSetGPIOVal
id tvWrite
id tvWriteCount
id tv_close
id type
id webServerBtn
layout activity_main
layout content_main
layout custom_toast
layout device_item
layout dialog_devicelist
layout dialog_devicelist_item
layout dialog_gpio
layout dialog_gpio_item
layout dialog_serial
layout item_floating
layout my_spinner_textview
layout serial_item
menu menu_main
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string action_settings
string app_name
string first_fragment_label
string next
string previous
string second_fragment_label
style Base.Theme.Floating
style Theme.Floating
xml backup_rules
xml data_extraction_rules
xml device_filter
