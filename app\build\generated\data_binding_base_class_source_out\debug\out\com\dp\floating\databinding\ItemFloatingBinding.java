// Generated by view binder compiler. Do not edit!
package com.dp.floating.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.dp.floating.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFloatingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout cardItemFx;

  @NonNull
  public final TextView tvItemFx;

  private ItemFloatingBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout cardItemFx,
      @NonNull TextView tvItemFx) {
    this.rootView = rootView;
    this.cardItemFx = cardItemFx;
    this.tvItemFx = tvItemFx;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFloatingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFloatingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_floating, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFloatingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout cardItemFx = (LinearLayout) rootView;

      id = R.id.tvItemFx;
      TextView tvItemFx = ViewBindings.findChildViewById(rootView, id);
      if (tvItemFx == null) {
        break missingId;
      }

      return new ItemFloatingBinding((LinearLayout) rootView, cardItemFx, tvItemFx);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
