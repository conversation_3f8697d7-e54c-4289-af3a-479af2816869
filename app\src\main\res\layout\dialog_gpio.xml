<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/dp_5"
    android:orientation="vertical">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textSize="@dimen/sp_17"
        android:text="GPIO配置"
        android:textColor="@color/black"
        />
    <TextView
        android:id="@+id/tvDes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:textColor="@color/red"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/ll_border"
        android:padding="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_5"
        >
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvGPIO"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            >
            <TextView
                android:id="@+id/tvGetAllGPIO"
                android:text="读取GPIO配置"
                android:textColor="@color/blue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tvConfigGPIO"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                />
            <TextView
                android:id="@+id/tvConfigGPIO"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvGetAllGPIO"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="配置芯片GPIO"
                android:textColor="@color/blue"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            >
            <TextView
                android:id="@+id/tvSetGPIOVal"
                android:text="设置GPIO电平"
                android:textColor="@color/blue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tvGetGPIOVal"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                />
            <TextView
                android:id="@+id/tvGetGPIOVal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvSetGPIOVal"
                app:layout_constraintEnd_toEndOf="parent"
                android:text="获取GPIO电平"
                android:textColor="@color/blue"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>



    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_10"
        >

        <TextView
            android:id="@+id/tv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="关闭"
            android:textColor="@color/blue"
            android:textSize="@dimen/sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>