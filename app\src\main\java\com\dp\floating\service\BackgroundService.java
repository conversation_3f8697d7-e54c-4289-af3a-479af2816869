package com.dp.floating.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.hardware.usb.UsbDevice;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.dp.floating.MainActivity;
import com.dp.floating.R;
import com.dp.floating.WebServer;
import com.dp.floating.tools.FormatUtil;
import com.dp.floating.tools.LogUtil;
import com.dp.floating.ui.SerialBaudBean;
import com.dp.floating.ui.SerialEntity;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import cn.wch.uartlib.WCHUARTManager;
import cn.wch.uartlib.base.error.SerialErrorType;
import cn.wch.uartlib.callback.IDataCallback;
import cn.wch.uartlib.callback.IModemStatus;
import cn.wch.uartlib.callback.IUsbStateChange;
import cn.wch.uartlib.exception.ChipException;
import cn.wch.uartlib.exception.NoPermissionException;
import cn.wch.uartlib.exception.UartLibException;

public class BackgroundService extends Service implements WebServer.ButtonClickHandler {
    private static final String TAG = "BackgroundService";
    private static final String CHANNEL_ID = "BackgroundServiceChannel";
    private static final int NOTIFICATION_ID = 1;

    // 核心功能变量
    final Set<UsbDevice> devices = Collections.synchronizedSet(new HashSet<UsbDevice>());
    private HashMap<String, FileOutputStream> fileOutputStreamMap = new HashMap<>();
    private HashMap<String, Integer> readCountMap = new HashMap<>();
    private WebServer webServer;
    private SerialEntity serialEntity;
    private Thread readThread;
    private boolean flag = false;
    private final boolean useReadThread = false;
    private static boolean FILE_TEST = false;

    // Binder for activity communication
    private final IBinder binder = new LocalBinder();

    public class LocalBinder extends Binder {
        public BackgroundService getService() {
            return BackgroundService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "BackgroundService onCreate");
        
        createNotificationChannel();
        startForeground(NOTIFICATION_ID, createNotification());
        
        // 初始化核心功能
        initializeCore();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "BackgroundService onStartCommand");
        return START_STICKY; // 服务被杀死后自动重启
    }

    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "BackgroundService onDestroy");
        
        // 清理资源
        cleanup();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Background Service Channel",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }

    private Notification createNotification() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, 
                PendingIntent.FLAG_IMMUTABLE);

        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("USB串口服务")
                .setContentText("USB设备管理和Web服务器正在运行")
                .setSmallIcon(R.drawable.ic_launcher)
                .setContentIntent(pendingIntent)
                .build();
    }

    private void initializeCore() {
        try {
            // 检查USB Host功能
            if (!usbFeatureSupported()) {
                Log.e(TAG, "系统不支持USB Host功能");
                return;
            }

            // 启动Web服务器
            startWebServer();
            
            // 连接设备
            connectDevice();
            
            // 监测USB插拔状态
            monitorUSBState();
            
        } catch (Exception e) {
            Log.e(TAG, "初始化核心功能失败", e);
        }
    }

    private void cleanup() {
        // 停止读线程
        if (useReadThread) {
            stopReadThread();
        }

        // 关闭所有连接设备
        closeAll();
        
        // 停止Web服务器
        if (webServer != null) {
            webServer.stopServer();
            webServer = null;
        }
        
        // 释放资源
        WCHUARTManager.getInstance().close(getApplicationContext());
    }

    // ========== 核心功能方法 ==========

    public boolean usbFeatureSupported() {
        return getPackageManager().hasSystemFeature("android.hardware.usb.host");
    }

    public void connectDevice() {
        try {
            ArrayList<UsbDevice> usbDeviceArrayList = WCHUARTManager.getInstance().enumDevice();
            if (usbDeviceArrayList.size() == 0) {
                Log.d(TAG, "no matched devices");
                return;
            }
            open(usbDeviceArrayList.get(0));
        } catch (Exception e) {
            Log.d(TAG, e.getMessage());
        }
    }

    public ArrayList<UsbDevice> enumDevice() {
        try {
            return WCHUARTManager.getInstance().enumDevice();
        } catch (Exception e) {
            Log.d(TAG, e.getMessage());
            return new ArrayList<>();
        }
    }

    public void startWebServer() {
        try {
            if (webServer == null || !webServer.isAlive()) {
                webServer = new WebServer(this);
                String serverInfo = webServer.startServer();
                Log.i(TAG, "Web服务器启动成功: " + serverInfo);
                
                // 更新通知
                updateNotification("USB串口服务", "Web服务器已启动 - " + serverInfo);
            }
        } catch (Exception e) {
            Log.e(TAG, "启动Web服务器失败", e);
        }
    }

    public void stopWebServer() {
        if (webServer != null) {
            webServer.stopServer();
            webServer = null;
            Log.i(TAG, "Web服务器已停止");
            updateNotification("USB串口服务", "Web服务器已停止");
        }
    }

    public boolean isWebServerRunning() {
        return webServer != null && webServer.isAlive();
    }

    private void updateNotification(String title, String content) {
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                    .setContentTitle(title)
                    .setContentText(content)
                    .setSmallIcon(R.drawable.ic_launcher_foreground)
                    .build();
            notificationManager.notify(NOTIFICATION_ID, notification);
        }
    }

    // 发送数据方法
    public void send(String data) {
        if (serialEntity != null) {
            byte[] bytes = data.getBytes();
            int result = writeData(serialEntity.getUsbDevice(), serialEntity.getSerialNumber(), bytes, bytes.length);
            Log.d(TAG, "发送数据结果: " + result);
        } else {
            Log.w(TAG, "没有可用的串口设备");
        }
    }

    // ========== USB设备管理方法 ==========
    
    public void open(@NonNull UsbDevice usbDevice) {
        if (WCHUARTManager.getInstance().isConnected(usbDevice)) {
            Log.d(TAG, "当前设备已经打开");
            return;
        }
        try {
            boolean b = WCHUARTManager.getInstance().openDevice(usbDevice);
            if (b) {
                Log.d(TAG, "设备打开成功: " + usbDevice.getDeviceName());
                
                serialEntity = new SerialEntity(usbDevice, 0);

                SerialBaudBean serialBaudBean = new SerialBaudBean();
                serialBaudBean.setBaud(115200);
                serialBaudBean.setData(8);
                serialBaudBean.setStop(1);
                serialBaudBean.setParity(0);
                serialBaudBean.setFlow(false);

                setSerialParameter(serialEntity.getUsbDevice(), serialEntity.getSerialNumber(), serialBaudBean);

                // 初始化接收计数
                int serialCount = 0;
                try {
                    serialCount = WCHUARTManager.getInstance().getSerialCount(usbDevice);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                for (int i = 0; i < serialCount; i++) {
                    readCountMap.put(FormatUtil.getSerialKey(usbDevice, i), 0);
                }
                
                addToReadDeviceSet(usbDevice);
                registerModemStatusCallback(usbDevice);
                if (!useReadThread) {
                    registerDataCallback(usbDevice);
                }
                
                updateNotification("USB串口服务", "设备已连接: " + usbDevice.getDeviceName());
            } else {
                Log.e(TAG, "打开设备失败");
            }
        } catch (ChipException e) {
            LogUtil.d(e.getMessage());
        } catch (NoPermissionException e) {
            Log.e(TAG, "没有权限打开该设备");
        } catch (UartLibException e) {
            e.printStackTrace();
        }
    }

    private void setSerialParameter(@NonNull UsbDevice usbDevice, int serialNumber, @NonNull SerialBaudBean serialBaudBean) {
        try {
            boolean result = WCHUARTManager.getInstance().setSerialParameter(usbDevice, serialNumber,
                    serialBaudBean.getBaud(), serialBaudBean.getData(), serialBaudBean.getStop(),
                    serialBaudBean.getParity(), serialBaudBean.isFlow());
            Log.d(TAG, "设置串口参数结果: " + result);
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
    }

    private int writeData(UsbDevice usbDevice, int serialNumber, @NonNull byte[] data, int length) {
        try {
            return WCHUARTManager.getInstance().syncWriteData(usbDevice, serialNumber, data, length, 2000);
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
            return -2;
        }
    }

    private void addToReadDeviceSet(@NonNull UsbDevice usbDevice) {
        synchronized (devices) {
            devices.add(usbDevice);
        }
    }

    private void removeReadDataDevice(@NonNull UsbDevice usbDevice) {
        synchronized (devices) {
            devices.remove(usbDevice);
        }
    }

    private void registerModemStatusCallback(@NonNull UsbDevice usbDevice) {
        try {
            int serialCount = WCHUARTManager.getInstance().getSerialCount(usbDevice);
            for (int i = 0; i < serialCount; i++) {
                final int serialNumber = i;
                WCHUARTManager.getInstance().setModemStatusCallback(usbDevice, serialNumber, new IModemStatus() {
                    @Override
                    public void onModemStatusChanged(UsbDevice usbDevice, int serialNumber, byte[] modemStatus) {
                        Log.d(TAG, "Modem状态变化: " + usbDevice.getDeviceName() + " 串口" + serialNumber);
                    }
                });
            }
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
    }

    private void registerDataCallback(@NonNull UsbDevice usbDevice) {
        try {
            int serialCount = WCHUARTManager.getInstance().getSerialCount(usbDevice);
            for (int i = 0; i < serialCount; i++) {
                final int serialNumber = i;
                WCHUARTManager.getInstance().setDataCallback(usbDevice, serialNumber, new IDataCallback() {
                    @Override
                    public void onDataReceived(UsbDevice usbDevice, int serialNumber, byte[] data) {
                        String receivedData = new String(data);
                        Log.d(TAG, "接收到数据: " + receivedData + " 来自设备: " + usbDevice.getDeviceName() + " 串口: " + serialNumber);

                        // 更新接收计数
                        String key = FormatUtil.getSerialKey(usbDevice, serialNumber);
                        Integer count = readCountMap.get(key);
                        if (count == null) count = 0;
                        readCountMap.put(key, count + data.length);
                    }

                    @Override
                    public void onDataError(UsbDevice usbDevice, int serialNumber, SerialErrorType serialErrorType) {
                        Log.e(TAG, "数据错误: " + serialErrorType + " 设备: " + usbDevice.getDeviceName() + " 串口: " + serialNumber);
                    }
                });
            }
        } catch (Exception e) {
            LogUtil.d(e.getMessage());
        }
    }

    private void monitorUSBState() {
        WCHUARTManager.getInstance().setUsbStateListener(new IUsbStateChange() {
            @Override
            public void usbDeviceDetach(UsbDevice device) {
                Log.d(TAG, "设备移除: " + device.getDeviceName());
                removeReadDataDevice(device);
                if (FILE_TEST) {
                    cancelDeviceLinks(device);
                }
                updateNotification("USB串口服务", "设备已断开: " + device.getDeviceName());
            }

            @Override
            public void usbDeviceAttach(UsbDevice device) {
                Log.d(TAG, "设备插入: " + device.getDeviceName());
                updateNotification("USB串口服务", "检测到新设备: " + device.getDeviceName());
            }

            @Override
            public void usbDevicePermission(UsbDevice device, boolean result) {
                Log.d(TAG, "设备权限结果: " + device.getDeviceName() + " 结果: " + result);
            }
        });
    }

    private void cancelDeviceLinks(UsbDevice usbDevice) {
        if (fileOutputStreamMap == null) {
            return;
        }
        for (int i = 0; i < 8; i++) {
            FileOutputStream fileOutputStream = fileOutputStreamMap.get(FormatUtil.getSerialKey(usbDevice, i));
            if (fileOutputStream == null) {
                continue;
            }
            Log.d(TAG, "关闭文件: " + usbDevice.getDeviceName() + " " + i);
            try {
                fileOutputStream.flush();
                fileOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void closeAll() {
        try {
            ArrayList<UsbDevice> usbDeviceArrayList = WCHUARTManager.getInstance().enumDevice();
            for (UsbDevice usbDevice : usbDeviceArrayList) {
                if (WCHUARTManager.getInstance().isConnected(usbDevice)) {
                    WCHUARTManager.getInstance().disconnect(usbDevice);
                    Log.d(TAG, "断开设备: " + usbDevice.getDeviceName());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stopReadThread() {
        if (readThread != null && readThread.isAlive()) {
            flag = false;
        }
    }

    // 获取服务状态信息
    public String getServiceStatus() {
        StringBuilder status = new StringBuilder();
        status.append("服务状态: 运行中\n");
        status.append("Web服务器: ").append(isWebServerRunning() ? "运行中" : "已停止").append("\n");
        status.append("已连接设备数: ").append(devices.size()).append("\n");

        if (webServer != null && webServer.isAlive()) {
            // 这里可以添加获取Web服务器地址的方法
            status.append("Web服务器地址: 请查看日志");
        }

        return status.toString();
    }
}
