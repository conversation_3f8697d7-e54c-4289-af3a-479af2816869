<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\src\main\res"><file name="aaa" path="D:\code\andriod\floating\app\src\main\res\drawable\aaa.png" qualifiers="" type="drawable"/><file name="activity_main" path="D:\code\andriod\floating\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="content_main" path="D:\code\andriod\floating\app\src\main\res\layout\content_main.xml" qualifiers="" type="layout"/><file name="item_floating" path="D:\code\andriod\floating\app\src\main\res\layout\item_floating.xml" qualifiers="" type="layout"/><file name="menu_main" path="D:\code\andriod\floating\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\code\andriod\floating\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\code\andriod\floating\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="D:\code\andriod\floating\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="D:\code\andriod\floating\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="grey_light">#EBF0F3</color><color name="purple_700">#FF3700B3</color><color name="blue">#3a89d1</color><color name="red">#ff0000</color><color name="teal_700">#FF018786</color><color name="grey_dark">#4A4B4C</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="teal_200">#FF03DAC5</color><color name="grey">#CCCECF</color></file><file path="D:\code\andriod\floating\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen><dimen name="dp_90">90dp</dimen><dimen name="dp_300">300dp</dimen><dimen name="dp_100">100dp</dimen><dimen name="dp_70">70dp</dimen><dimen name="dp_120">120dp</dimen><dimen name="dp_350">350dp</dimen><dimen name="dp_250">250dp</dimen><dimen name="sp_16">16sp</dimen><dimen name="dp_5">5dp</dimen><dimen name="dp_10">10dp</dimen><dimen name="sp_15">15sp</dimen><dimen name="dp_35">35dp</dimen><dimen name="sp_17">17sp</dimen><dimen name="dp_40">40dp</dimen><dimen name="dp_50">50dp</dimen><dimen name="dp_20">20dp</dimen><dimen name="dp_30">30dp</dimen><dimen name="sp_10">10sp</dimen><dimen name="dp_15">15dp</dimen><dimen name="sp_12">12sp</dimen><dimen name="sp_11">11sp</dimen><dimen name="sp_14">14sp</dimen><dimen name="sp_13">13sp</dimen></file><file path="D:\code\andriod\floating\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">floating</string><string name="action_settings">Settings</string><string name="first_fragment_label">First Fragment</string><string name="second_fragment_label">Second Fragment</string><string name="next">Next</string><string name="previous">Previous</string><string-array name="parity">
        <item>无</item>
        <item>奇校验</item>
        <item>偶校验</item>
        <item>标志位</item>
        <item>空白位</item>
    </string-array><string-array name="baud">
        <item>50</item>
        <item>75</item>
        <item>100</item>
        <item>110</item>
        <item>150</item>
        <item>300</item>
        <item>600</item>
        <item>900</item>
        <item>1200</item>
        <item>1800</item>
        <item>2400</item>
        <item>3600</item>
        <item>4800</item>
        <item>9600</item>
        <item>14400</item>
        <item>19200</item>
        <item>28800</item>
        <item>33600</item>
        <item>38400</item>
        <item>56000</item>
        <item>57600</item>
        <item>76800</item>
        <item>115200</item>
        <item>128000</item>
        <item>153600</item>
        <item>230400</item>
        <item>256000</item>
        <item>307200</item>
        <item>460800</item>
        <item>921600</item>
        <item>1000000</item>
        <item>1500000</item>
        <item>2000000</item>
        <item>3000000</item>
        <item>4000000</item>
        <item>6000000</item>
        <item>9000000</item>
    </string-array><string-array name="stop">
        <item>1</item>
        <item>2</item>
    </string-array><string-array name="data">
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>

    </string-array></file><file path="D:\code\andriod\floating\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Floating" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Floating" parent="Base.Theme.Floating"/></file><file path="D:\code\andriod\floating\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="D:\code\andriod\floating\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Floating" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file path="D:\code\andriod\floating\app\src\main\res\values-v23\themes.xml" qualifiers="v23"><style name="Theme.Floating" parent="Base.Theme.Floating">
        
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">?attr/isLightTheme</item>
    </style></file><file path="D:\code\andriod\floating\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file path="D:\code\andriod\floating\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file name="backup_rules" path="D:\code\andriod\floating\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\code\andriod\floating\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_launcher" path="D:\code\andriod\floating\app\src\main\res\drawable\ic_launcher.jpg" qualifiers="" type="drawable"/><file name="custom_toast" path="D:\code\andriod\floating\app\src\main\res\layout\custom_toast.xml" qualifiers="" type="layout"/><file name="device_item" path="D:\code\andriod\floating\app\src\main\res\layout\device_item.xml" qualifiers="" type="layout"/><file name="dialog_devicelist" path="D:\code\andriod\floating\app\src\main\res\layout\dialog_devicelist.xml" qualifiers="" type="layout"/><file name="dialog_devicelist_item" path="D:\code\andriod\floating\app\src\main\res\layout\dialog_devicelist_item.xml" qualifiers="" type="layout"/><file name="dialog_gpio" path="D:\code\andriod\floating\app\src\main\res\layout\dialog_gpio.xml" qualifiers="" type="layout"/><file name="dialog_gpio_item" path="D:\code\andriod\floating\app\src\main\res\layout\dialog_gpio_item.xml" qualifiers="" type="layout"/><file name="dialog_serial" path="D:\code\andriod\floating\app\src\main\res\layout\dialog_serial.xml" qualifiers="" type="layout"/><file name="my_spinner_textview" path="D:\code\andriod\floating\app\src\main\res\layout\my_spinner_textview.xml" qualifiers="" type="layout"/><file name="serial_item" path="D:\code\andriod\floating\app\src\main\res\layout\serial_item.xml" qualifiers="" type="layout"/><file name="device_filter" path="D:\code\andriod\floating\app\src\main\res\xml\device_filter.xml" qualifiers="" type="xml"/><file name="bg_spinner" path="D:\code\andriod\floating\app\src\main\res\drawable\bg_spinner.xml" qualifiers="" type="drawable"/><file name="btn_round" path="D:\code\andriod\floating\app\src\main\res\drawable\btn_round.xml" qualifiers="" type="drawable"/><file name="ll_border" path="D:\code\andriod\floating\app\src\main\res\drawable\ll_border.xml" qualifiers="" type="drawable"/><file name="ll_round" path="D:\code\andriod\floating\app\src\main\res\drawable\ll_round.xml" qualifiers="" type="drawable"/><file name="ll_round_black" path="D:\code\andriod\floating\app\src\main\res\drawable\ll_round_black.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\code\andriod\floating\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>