<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/ll_round"
    android:padding="@dimen/dp_10"
    android:layout_marginBottom="@dimen/dp_5"

    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
        <TextView
            android:id="@+id/tvSerialDescription"
            android:textStyle="bold"
            android:textSize="@dimen/sp_14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_gravity="center"
            />
        <TextView
            android:id="@+id/tvSerialInfo"
            android:textSize="@dimen/sp_14"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_5"
            android:text="未设置串口参数"
            android:layout_gravity="center"
            android:textColor="@color/red"
            />

        <com.dp.floating.ui.CustomTextView
            android:id="@+id/tvSerialConfig"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="设置串口参数"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/sp_14"
            android:enabled="true"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="horizontal"
        >
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbDTR"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="DTR"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbRTS"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="RTS"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbBreak"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="BREAK"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:textSize="12sp"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="horizontal"
        >
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbDCD"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="DCD"
            android:enabled="false"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbDSR"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="DSR"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:enabled="false"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbCTS"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="CTS"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:enabled="false"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbRing"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="RING"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:enabled="false"
            android:textSize="12sp"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="horizontal"
        >
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbOverrun"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Overrun Error"
            android:enabled="false"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:textSize="12sp"
            />
        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbParity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Parity Error"
            android:minHeight="0dp"
            android:minWidth="0dp"
            android:enabled="false"
            android:textSize="12sp"
            />

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbFrame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:enabled="false"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:text="Frame Error"
            android:textSize="12sp" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="horizontal"
        >
        <com.dp.floating.ui.CustomTextView
            android:id="@+id/queryErrorStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="query error status"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/sp_14"
            android:enabled="true"
            />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:orientation="vertical"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <TextView
                android:text="发送计数：0字节"
                android:id="@+id/tvWriteCount"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                />
            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/scWrite"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_20"
                android:textOff="关"
                android:textOn="开"
                android:checked="true"
                android:text="HEX"
                app:switchPadding="@dimen/dp_5"
                app:switchMinWidth="@dimen/dp_50"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <EditText
                android:id="@+id/send_data"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50"
                android:gravity="start|top"
                android:background="@drawable/ll_border"
                android:hint="输入数据"
                android:text="push_bat 0"
                android:layout_marginTop="@dimen/dp_5"
                />
        </LinearLayout>

    </LinearLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp_5"
        >

        <com.dp.floating.ui.CustomTextView
            android:id="@+id/tvClearWrite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="清空"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/sp_14"
            />

        <com.dp.floating.ui.CustomTextView
            android:id="@+id/tvWrite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="发送"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textSize="@dimen/sp_14"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>