1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dp.floating"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:5-78
11-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:22-75
12    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
12-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:5-80
12-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:22-77
13    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
13-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:9:5-81
13-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:9:22-78
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:5-80
14-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:22-77
15    <uses-permission android:name="android.hardware.usb.accessory" />
15-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:5-70
15-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:22-67
16    <uses-permission android:name="android.hardware.usb.host" />
16-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:5-65
16-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:22-62
17    <uses-permission android:name="android.permission.INTERNET" />
17-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:5-67
17-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:5-79
18-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:22-76
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:5-77
19-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:22-74
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:5-68
20-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:22-65
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:18:5-80
21-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:18:22-78
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:5-80
22-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:22-77
23
24    <uses-feature
24-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:20:5-22:35
25        android:name="android.hardware.usb.host"
25-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:21:9-49
26        android:required="true" />
26-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:22:9-32
27
28    <permission
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:24:5-72:19
35        android:name="com.dp.floating.CustomJavaApplication"
35-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:25:9-46
36        android:allowBackup="true"
36-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:26:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:27:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="true"
41        android:fullBackupContent="@xml/backup_rules"
41-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:28:9-54
42        android:icon="@drawable/ic_launcher"
42-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:29:9-45
43        android:label="@string/app_name"
43-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:30:9-41
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:31:9-54
45        android:supportsRtl="true"
45-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:32:9-35
46        android:testOnly="true"
47        android:theme="@style/Theme.Floating" >
47-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:33:9-46
48        <activity
48-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:35:9-44:20
49            android:name="com.dp.floating.MainActivity"
49-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:36:13-41
50            android:exported="true"
50-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:37:13-36
51            android:theme="@style/Theme.Floating" >
51-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:38:13-50
52            <intent-filter>
52-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:39:13-43:29
53                <action android:name="android.intent.action.MAIN" />
53-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:40:17-69
53-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:40:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:17-77
55-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:27-74
56            </intent-filter>
57        </activity>
58
59        <receiver
59-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:47:9-54:20
60            android:name="com.dp.floating.service.LauncherReceiver"
60-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:48:13-53
61            android:exported="true" >
61-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:49:13-36
62            <intent-filter>
62-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:50:13-53:29
63                <action android:name="android.intent.action.BOOT_COMPLETED" />
63-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:51:17-79
63-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:51:25-76
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:17-77
65-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:42:27-74
66            </intent-filter>
67        </receiver>
68
69        <service
69-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:56:9-64:19
70            android:name="com.dp.floating.service.LauncherService"
70-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:57:13-52
71            android:enabled="true"
71-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:58:13-35
72            android:exported="true"
72-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:59:13-36
73            android:process="com.test.service" >
73-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:60:13-47
74            <intent-filter>
74-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:61:13-63:29
75                <action android:name="com.test.Service" />
75-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:62:17-59
75-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:62:25-56
76            </intent-filter>
77        </service>
78        <service
78-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:66:9-71:19
79            android:name="com.dp.floating.service.BackgroundService"
79-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:67:13-54
80            android:enabled="true"
80-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:68:13-35
81            android:exported="false"
81-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:69:13-37
82            android:foregroundServiceType="dataSync" >
82-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:70:13-53
83        </service>
84
85        <provider
85-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:8:9-12:43
86            android:name="com.petterp.floatingx.assist.FxContentProvider"
86-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:9:13-74
87            android:authorities="com.dp.floating.fx.provider"
87-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:10:13-63
88            android:exported="false"
88-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:11:13-37
89            android:multiprocess="true" />
89-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:12:13-40
90        <provider
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
91            android:name="androidx.startup.InitializationProvider"
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
92            android:authorities="com.dp.floating.androidx-startup"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
93            android:exported="false" >
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
94            <meta-data
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.emoji2.text.EmojiCompatInitializer"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
96                android:value="androidx.startup" />
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
98-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
99                android:value="androidx.startup" />
99-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
102                android:value="androidx.startup" />
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
103        </provider>
104
105        <uses-library
105-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
106            android:name="androidx.window.extensions"
106-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
107            android:required="false" />
107-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
108        <uses-library
108-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
109            android:name="androidx.window.sidecar"
109-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
110            android:required="false" />
110-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
111
112        <receiver
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
113            android:name="androidx.profileinstaller.ProfileInstallReceiver"
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
114            android:directBootAware="false"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
115            android:enabled="true"
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
116            android:exported="true"
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
117            android:permission="android.permission.DUMP" >
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
119                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
122                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
125                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
128                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
129            </intent-filter>
130        </receiver>
131    </application>
132
133</manifest>
