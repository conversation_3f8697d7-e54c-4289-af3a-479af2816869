1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dp.floating"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:5-77
11-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:6:22-74
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:5-78
12-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:7:22-75
13    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
13-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:8:5-80
13-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:8:22-77
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:5-81
14-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:10:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:5-80
15-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:22-77
16    <uses-permission android:name="android.hardware.usb.accessory" />
16-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:5-70
16-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.hardware.usb.host" />
17-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:5-65
17-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:13:22-62
18    <uses-permission android:name="android.permission.INTERNET" />
18-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:5-67
18-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:5-79
19-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:15:22-76
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:5-77
20-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:16:22-74
21    <uses-permission android:name="android.permission.WAKE_LOCK" />
21-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:17:5-68
21-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:17:22-65
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:19:5-80
22-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:19:22-78
23    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
23-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:5-80
23-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:11:22-77
24
25    <uses-feature
25-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:21:5-23:35
26        android:name="android.hardware.usb.host"
26-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:22:9-49
27        android:required="true" />
27-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:23:9-32
28
29    <permission
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.dp.floating.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:25:5-73:19
36        android:name="com.dp.floating.CustomJavaApplication"
36-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:26:9-46
37        android:allowBackup="true"
37-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:27:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69dafdeef1b7d5ba8718d511efd7f90d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:28:9-65
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:fullBackupContent="@xml/backup_rules"
42-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:29:9-54
43        android:icon="@drawable/ic_launcher"
43-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:30:9-45
44        android:label="@string/app_name"
44-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:31:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:32:9-54
46        android:supportsRtl="true"
46-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:33:9-35
47        android:testOnly="true"
48        android:theme="@style/Theme.Floating" >
48-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:34:9-46
49        <activity
49-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:36:9-45:20
50            android:name="com.dp.floating.MainActivity"
50-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:37:13-41
51            android:exported="true"
51-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:38:13-36
52            android:theme="@style/Theme.Floating" >
52-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:39:13-50
53            <intent-filter>
53-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:40:13-44:29
54                <action android:name="android.intent.action.MAIN" />
54-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:41:17-69
54-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:41:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:43:17-77
56-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:43:27-74
57            </intent-filter>
58        </activity>
59
60        <receiver
60-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:48:9-55:20
61            android:name="com.dp.floating.service.LauncherReceiver"
61-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:49:13-53
62            android:exported="true" >
62-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:50:13-36
63            <intent-filter>
63-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:51:13-54:29
64                <action android:name="android.intent.action.BOOT_COMPLETED" />
64-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:52:17-79
64-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:52:25-76
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:43:17-77
66-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:43:27-74
67            </intent-filter>
68        </receiver>
69
70        <service
70-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:57:9-65:19
71            android:name="com.dp.floating.service.LauncherService"
71-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:58:13-52
72            android:enabled="true"
72-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:59:13-35
73            android:exported="true"
73-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:60:13-36
74            android:process="com.test.service" >
74-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:61:13-47
75            <intent-filter>
75-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:62:13-64:29
76                <action android:name="com.test.Service" />
76-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:63:17-59
76-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:63:25-56
77            </intent-filter>
78        </service>
79        <service
79-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:67:9-72:19
80            android:name="com.dp.floating.service.BackgroundService"
80-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:68:13-54
81            android:enabled="true"
81-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:69:13-35
82            android:exported="false"
82-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:70:13-37
83            android:foregroundServiceType="dataSync" >
83-->D:\code\andriod\floating\app\src\main\AndroidManifest.xml:71:13-53
84        </service>
85
86        <provider
86-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:8:9-12:43
87            android:name="com.petterp.floatingx.assist.FxContentProvider"
87-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:9:13-74
88            android:authorities="com.dp.floating.fx.provider"
88-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:10:13-63
89            android:exported="false"
89-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:11:13-37
90            android:multiprocess="true" />
90-->[io.github.petterpx:floatingx:2.3.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1fb06578a9d34f6437e36733ec09aec\transformed\floatingx-2.3.5\AndroidManifest.xml:12:13-40
91        <provider
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.dp.floating.androidx-startup"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a649a9db255a67a05b44b3ffc0f73c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f54fa2823a538df237dc3e36709269\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <uses-library
106-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
107            android:name="androidx.window.extensions"
107-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
108            android:required="false" />
108-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
109        <uses-library
109-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
110            android:name="androidx.window.sidecar"
110-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
111            android:required="false" />
111-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9720fe12b26f97b8a368c0c08b5d8968\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
112
113        <receiver
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
114            android:name="androidx.profileinstaller.ProfileInstallReceiver"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
115            android:directBootAware="false"
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
116            android:enabled="true"
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
117            android:exported="true"
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
118            android:permission="android.permission.DUMP" >
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
120                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
121            </intent-filter>
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
123                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
126                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
127            </intent-filter>
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
129                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985a91971bd4b050a62d06f6ec4547e6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
130            </intent-filter>
131        </receiver>
132    </application>
133
134</manifest>
