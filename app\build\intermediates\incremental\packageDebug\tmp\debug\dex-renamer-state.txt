#Wed Jun 25 17:03:06 CST 2025
base.0=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.3=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.4=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.5=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\6\\classes.dex
base.6=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.7=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\9\\classes.dex
base.8=D\:\\code\\andriod\\floating\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=10/classes.dex
path.3=1/classes.dex
path.4=2/classes.dex
path.5=6/classes.dex
path.6=8/classes.dex
path.7=9/classes.dex
path.8=classes2.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
